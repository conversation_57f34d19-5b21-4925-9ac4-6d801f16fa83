import pandas as pd
import numpy as np
import re
import time

def fast_readdd():
    """
    优化版本的readdd函数 - 直接替换原函数
    主要优化：
    1. 避免重复读取Excel文件
    2. 使用向量化操作替代循环
    3. 减少不必要的dask操作
    4. 优化正则表达式匹配
    5. 减少merge操作次数
    """
    
    start_time = time.time()
    print("开始优化处理...")
    
    # 1. 预处理文件路径 - 向量化操作
    data11_processed = data11.copy()
    data11_processed['文件路径'] = data11_processed['文件路径'].str.replace(r'^L:\\审计提资项目资料\\', '', regex=True)
    data11_processed['文件名'] = data11_processed['文件路径'].str.rsplit('\\', n=1).str.get(-1)
    
    print(f"步骤1完成: {time.time() - start_time:.2f}秒")
    
    # 2. 预编译正则表达式模式
    dept_list = ['数字应用部', '数据运营部', '安全运行部', '平台运维部', '架构与创新部', '信息调度与服务部']
    dept_pattern = '|'.join(dept_list)
    
    # 清理合同数据
    htdf_clean = [str(x) for x in htdf if x and str(x) != 'nan']
    htdf2_clean = [str(x) for x in htdf2 if x and str(x) != 'nan']
    all_contracts = htdf_clean + htdf2_clean
    
    # 清理项目编码数据
    pronum_clean = [str(x) for x in pronum.dropna() if x and str(x) != 'nan']
    
    print(f"步骤2完成: {time.time() - start_time:.2f}秒")
    
    # 3. 批量正则匹配 - 一次性处理
    # 部门匹配
    data11_processed['分部'] = data11_processed['文件路径'].str.extract(f'({dept_pattern})', expand=False)
    
    # 合同匹配
    if all_contracts:
        contract_pattern = '|'.join(re.escape(contract) for contract in all_contracts)
        data11_processed['合同名称'] = data11_processed['文件路径'].str.extract(f'({contract_pattern})', expand=False)
    else:
        data11_processed['合同名称'] = ''
    
    # 项目编码匹配
    if pronum_clean:
        pronum_pattern = '|'.join(re.escape(code) for code in pronum_clean)
        data11_processed['项目编码'] = data11_processed['文件路径'].str.extract(f'({pronum_pattern})', expand=False)
    else:
        data11_processed['项目编码'] = ''
    
    print(f"步骤3完成: {time.time() - start_time:.2f}秒")
    
    # 4. 优化项目名称匹配 - 避免重复读取和循环
    data12 = pd.read_excel(fileDF, sheet_name='2025-06-13', engine='calamine')
    data12['文件路径'] = data12['文件路径'].str.replace(r'^L:\\审计提资项目资料\\', '', regex=True)
    data12['项目名称'] = ''
    
    # 清理项目名称列表
    pronameall_clean = [str(name) for name in pronameall if name and str(name) != 'nan']
    
    # 使用向量化操作进行项目名称匹配
    for project_name in pronameall_clean:
        try:
            mask = data12['文件路径'].str.contains(re.escape(project_name), na=False)
            empty_mask = data12['项目名称'] == ''
            data12.loc[mask & empty_mask, '项目名称'] = project_name
        except Exception as e:
            print(f"处理项目名称 '{project_name}' 时出错: {e}")
            continue
    
    # 只保留匹配到项目名称的记录
    df1 = data12[data12['项目名称'] != ''].copy()
    
    print(f"步骤4完成: {time.time() - start_time:.2f}秒")
    
    # 5. 优化合并操作 - 使用pandas而不是dask
    # 处理文件大小
    if '文件大小' in data11_processed.columns:
        data11_processed['文件大小'] = pd.to_numeric(data11_processed['文件大小'], errors='coerce').fillna(0) / 10000
    
    # 一次性合并数据
    if not df1.empty:
        d5 = pd.merge(data11_processed, df1[['文件路径', '项目名称']], on='文件路径', how='left')
    else:
        d5 = data11_processed.copy()
        d5['项目名称'] = ''
    
    # 合并项目类型信息
    if not ptypeall.empty and '项目名称' in ptypeall.columns:
        d5 = pd.merge(d5, ptypeall, on='项目名称', how='left')
    else:
        d5['类型'] = ''
        d5['项目负责人'] = ''
    
    print(f"步骤5完成: {time.time() - start_time:.2f}秒")
    
    # 6. 清理和整理数据
    d5 = d5.drop_duplicates('文件路径')
    
    # 选择最终列
    final_columns = ['文件路径', '文件大小', '文件类型', '分部', '合同名称', '项目编码', '项目名称', '类型', '项目负责人', '文件名']
    available_columns = [col for col in final_columns if col in d5.columns]
    d5 = d5[available_columns].fillna('')
    
    # 7. 生成返回数据
    d5_projectnames = d5['项目名称'].values.tolist()
    d5_filename = pd.DataFrame(d5['项目名称'].dropna().unique())
    d5_filepathname = d5[['文件路径', '文件名']].values.tolist()
    df1_type = d5['类型'].values.tolist()
    
    print(f"步骤6完成: {time.time() - start_time:.2f}秒")
    
    # 8. 保存结果 - 优化版本
    try:
        out11 = '优化测试结果.xlsx'
        with pd.ExcelWriter(out11, engine='openpyxl') as writer:
            d5.to_excel(writer, sheet_name='Sheet1', index=False)
            if not df1.empty:
                df1.to_excel(writer, sheet_name='Sheet2', index=False)
        print(f"结果已保存到: {out11}")
    except Exception as e:
        print(f"保存Excel失败: {e}")
        # 备选方案
        d5.to_csv('优化测试结果.csv', index=False, encoding='utf-8-sig')
        print("已保存为CSV格式")
    
    total_time = time.time() - start_time
    print(f"总优化处理时间: {total_time:.2f}秒")
    
    return d5, d5_filename, df1, d5_filepathname, df1_type, d5_projectnames


def ultra_fast_readdd():
    """
    超级优化版本 - 最大化性能
    """
    start_time = time.time()
    print("开始超级优化处理...")
    
    # 1. 最小化数据复制
    data11_processed = data11.copy()
    
    # 2. 批量字符串操作
    data11_processed['文件路径'] = data11_processed['文件路径'].str.replace(r'^L:\\审计提资项目资料\\', '', regex=True)
    data11_processed['文件名'] = data11_processed['文件路径'].str.rsplit('\\', n=1).str.get(-1)
    
    # 3. 预处理所有模式
    dept_pattern = '数字应用部|数据运营部|安全运行部|平台运维部|架构与创新部|信息调度与服务部'
    
    # 清理数据
    all_contracts = [str(x) for x in list(htdf) + list(htdf2) if x and str(x) != 'nan']
    pronum_list = [str(x) for x in pronum.dropna() if x and str(x) != 'nan']
    
    # 4. 批量正则匹配
    data11_processed['分部'] = data11_processed['文件路径'].str.extract(f'({dept_pattern})', expand=False).fillna('')
    
    if all_contracts:
        contract_pattern = '|'.join(re.escape(c) for c in all_contracts)
        data11_processed['合同名称'] = data11_processed['文件路径'].str.extract(f'({contract_pattern})', expand=False).fillna('')
    else:
        data11_processed['合同名称'] = ''
    
    if pronum_list:
        pronum_pattern = '|'.join(re.escape(p) for p in pronum_list)
        data11_processed['项目编码'] = data11_processed['文件路径'].str.extract(f'({pronum_pattern})', expand=False).fillna('')
    else:
        data11_processed['项目编码'] = ''
    
    # 5. 项目名称匹配
    data12 = pd.read_excel(fileDF, sheet_name='2025-06-13', engine='calamine')
    data12['文件路径'] = data12['文件路径'].str.replace(r'^L:\\审计提资项目资料\\', '', regex=True)
    data12['项目名称'] = ''
    
    # 高效匹配
    project_names = [str(x) for x in pronameall if x and str(x) != 'nan']
    for project_name in project_names:
        mask = data12['文件路径'].str.contains(re.escape(project_name), na=False)
        data12.loc[mask & (data12['项目名称'] == ''), '项目名称'] = project_name
    
    df1 = data12[data12['项目名称'] != ''].copy()
    
    # 6. 快速合并
    data11_processed['文件大小'] = pd.to_numeric(data11_processed['文件大小'], errors='coerce').fillna(0) / 10000
    
    d5 = pd.merge(data11_processed, df1[['文件路径', '项目名称']], on='文件路径', how='left')
    
    if not ptypeall.empty:
        d5 = pd.merge(d5, ptypeall, on='项目名称', how='left')
    else:
        d5['类型'] = ''
        d5['项目负责人'] = ''
    
    # 7. 最终处理
    d5 = d5.drop_duplicates('文件路径').fillna('')
    
    # 选择列
    cols = ['文件路径', '文件大小', '文件类型', '分部', '合同名称', '项目编码', '项目名称', '类型', '项目负责人', '文件名']
    d5 = d5[[col for col in cols if col in d5.columns]]
    
    # 8. 生成返回值
    d5_projectnames = d5['项目名称'].values.tolist()
    d5_filename = pd.DataFrame(d5['项目名称'].dropna().unique())
    d5_filepathname = d5[['文件路径', '文件名']].values.tolist()
    df1_type = d5['类型'].values.tolist()
    
    # 9. 快速保存
    out11 = '超级优化结果.xlsx'
    with pd.ExcelWriter(out11, engine='openpyxl') as writer:
        d5.to_excel(writer, sheet_name='Sheet1', index=False)
        if not df1.empty:
            df1.to_excel(writer, sheet_name='Sheet2', index=False)
    
    total_time = time.time() - start_time
    print(f"超级优化总时间: {total_time:.2f}秒")
    
    return d5, d5_filename, df1, d5_filepathname, df1_type, d5_projectnames


# 性能对比函数
def compare_performance():
    """
    性能对比测试
    """
    print("=== 性能对比测试 ===")
    
    # 原版本（需要您提供原始的readdd函数）
    print("运行原版本...")
    # original_start = time.time()
    # original_result = readdd()  # 原始函数
    # original_time = time.time() - original_start
    
    # 优化版本
    print("运行优化版本...")
    optimized_start = time.time()
    optimized_result = fast_readdd()
    optimized_time = time.time() - optimized_start
    
    # 超级优化版本
    print("运行超级优化版本...")
    ultra_start = time.time()
    ultra_result = ultra_fast_readdd()
    ultra_time = time.time() - ultra_start
    
    print(f"\n=== 性能对比结果 ===")
    # print(f"原版本耗时: {original_time:.2f}秒")
    print(f"优化版本耗时: {optimized_time:.2f}秒")
    print(f"超级优化版本耗时: {ultra_time:.2f}秒")
    # print(f"优化提升: {((original_time - optimized_time) / original_time * 100):.1f}%")
    # print(f"超级优化提升: {((original_time - ultra_time) / original_time * 100):.1f}%")


if __name__ == "__main__":
    print("请在您的主程序中调用以下函数之一:")
    print("1. fast_readdd() - 优化版本")
    print("2. ultra_fast_readdd() - 超级优化版本")
    print("3. compare_performance() - 性能对比")
