import pandas as pd
import numpy as np
import re
from typing import List, <PERSON><PERSON>

def optimized_readdd(data11, htdf, htdf2, pronum, pronameall, ptypeall, fileDF):
    """
    优化版本的readdd函数
    主要优化点：
    1. 避免重复读取Excel文件
    2. 使用向量化操作替代循环
    3. 减少merge操作次数
    4. 优化正则表达式匹配
    """
    
    print("开始优化处理...")
    start_time = pd.Timestamp.now()
    
    # 1. 预处理文件路径 - 向量化操作
    data11 = data11.copy()  # 避免修改原数据
    data11['文件路径'] = data11['文件路径'].str.replace(r'^L:\\审计提资项目资料\\', '', regex=True)
    data11['文件名'] = data11['文件路径'].str.rsplit('\\', n=1).str.get(-1)
    
    print(f"文件路径预处理完成: {(pd.Timestamp.now() - start_time).total_seconds():.2f}秒")
    
    # 2. 预编译所有正则表达式模式
    dept_list = ['数字应用部', '数据运营部', '安全运行部', '平台运维部', '架构与创新部', '信息调度与服务部']
    dept_pattern = '|'.join(dept_list)
    
    # 安全处理空值和重复项
    htdf_clean = [str(x) for x in htdf if x and str(x) != 'nan']
    htdf2_clean = [str(x) for x in htdf2 if x and str(x) != 'nan']
    all_contracts = list(set(htdf_clean + htdf2_clean))  # 去重
    
    pronum_clean = [str(x) for x in pronum.dropna() if x and str(x) != 'nan']
    pronum_clean = list(set(pronum_clean))  # 去重
    
    print(f"模式预处理完成: {(pd.Timestamp.now() - start_time).total_seconds():.2f}秒")
    
    # 3. 批量正则匹配 - 一次性处理所有模式
    # 部门匹配
    if dept_pattern:
        data11['分部'] = data11['文件路径'].str.extract(f'({dept_pattern})', expand=False)
    else:
        data11['分部'] = ''
    
    # 合同匹配
    if all_contracts:
        contract_pattern = '|'.join(re.escape(contract) for contract in all_contracts)
        data11['合同名称'] = data11['文件路径'].str.extract(f'({contract_pattern})', expand=False)
    else:
        data11['合同名称'] = ''
    
    # 项目编码匹配
    if pronum_clean:
        pronum_pattern = '|'.join(re.escape(code) for code in pronum_clean)
        data11['项目编码'] = data11['文件路径'].str.extract(f'({pronum_pattern})', expand=False)
    else:
        data11['项目编码'] = ''
    
    print(f"正则匹配完成: {(pd.Timestamp.now() - start_time).total_seconds():.2f}秒")
    
    # 4. 优化项目名称匹配 - 避免重复读取Excel
    # 只读取一次Excel文件
    data12 = pd.read_excel(fileDF, sheet_name='2025-06-13', engine='calamine')
    data12['文件路径'] = data12['文件路径'].str.replace(r'^L:\\审计提资项目资料\\', '', regex=True)
    
    # 使用向量化操作进行项目名称匹配
    data12['项目名称'] = ''
    
    # 预处理项目名称列表
    pronameall_clean = [str(name) for name in pronameall if name and str(name) != 'nan']
    pronameall_clean = list(set(pronameall_clean))  # 去重
    
    # 批量匹配项目名称
    for project_name in pronameall_clean:
        try:
            # 使用向量化操作
            mask = data12['文件路径'].str.contains(re.escape(project_name), na=False)
            empty_mask = data12['项目名称'] == ''
            data12.loc[mask & empty_mask, '项目名称'] = project_name
        except Exception as e:
            print(f"处理项目名称 '{project_name}' 时出错: {e}")
            continue
    
    # 只保留匹配到项目名称的记录
    df1 = data12[data12['项目名称'] != ''].copy()
    
    print(f"项目名称匹配完成: {(pd.Timestamp.now() - start_time).total_seconds():.2f}秒")
    
    # 5. 优化合并操作 - 减少merge次数
    # 处理文件大小
    if '文件大小' in data11.columns:
        data11['文件大小'] = pd.to_numeric(data11['文件大小'], errors='coerce').fillna(0) / 10000
    else:
        data11['文件大小'] = 0
    
    # 一次性合并所有数据
    if not df1.empty:
        # 使用pandas而不是dask进行合并，速度更快
        d5 = pd.merge(data11, df1[['文件路径', '项目名称']], on='文件路径', how='left')
    else:
        d5 = data11.copy()
        d5['项目名称'] = ''
    
    # 合并项目类型信息
    if not ptypeall.empty and '项目名称' in ptypeall.columns:
        # 确保ptypeall中的数据类型正确
        ptypeall_clean = ptypeall.copy()
        for col in ptypeall_clean.columns:
            if ptypeall_clean[col].dtype == 'object':
                ptypeall_clean[col] = ptypeall_clean[col].astype(str)
        
        d5 = pd.merge(d5, ptypeall_clean, on='项目名称', how='left')
    else:
        d5['类型'] = ''
        d5['项目负责人'] = ''
    
    print(f"数据合并完成: {(pd.Timestamp.now() - start_time).total_seconds():.2f}秒")
    
    # 6. 清理和整理最终数据
    d5 = d5.drop_duplicates('文件路径')
    
    # 选择最终列
    final_columns = ['文件路径', '文件大小', '文件类型', '分部', '合同名称', '项目编码', '项目名称', '类型', '项目负责人', '文件名']
    available_columns = [col for col in final_columns if col in d5.columns]
    d5 = d5[available_columns].fillna('')
    
    # 7. 生成返回数据
    d5_projectnames = d5['项目名称'].tolist()
    d5_filename = pd.DataFrame(d5['项目名称'].dropna().unique(), columns=['项目名称'])
    d5_filepathname = d5[['文件路径', '文件名']].values.tolist()
    df1_type = d5['类型'].tolist()
    
    print(f"数据整理完成: {(pd.Timestamp.now() - start_time).total_seconds():.2f}秒")
    
    # 8. 保存结果到Excel - 优化版本
    try:
        output_file = '优化测试结果.xlsx'
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            d5.to_excel(writer, sheet_name='处理结果', index=False)
            if not df1.empty:
                df1.to_excel(writer, sheet_name='项目匹配', index=False)
        
        print(f"结果已保存到: {output_file}")
    except Exception as e:
        print(f"保存Excel失败: {e}")
        # 备选：保存为CSV
        d5.to_csv('优化测试结果.csv', index=False, encoding='utf-8-sig')
        print("已保存为CSV格式")
    
    total_time = (pd.Timestamp.now() - start_time).total_seconds()
    print(f"总处理时间: {total_time:.2f}秒")
    
    return d5, d5_filename, df1, d5_filepathname, df1_type, d5_projectnames


def super_fast_readdd(data11, htdf, htdf2, pronum, pronameall, ptypeall, fileDF):
    """
    超级优化版本 - 最大化性能
    """
    print("开始超级优化处理...")
    start_time = pd.Timestamp.now()
    
    # 1. 数据预处理 - 最小化操作
    data11 = data11.copy()
    data11['文件路径'] = data11['文件路径'].str.replace(r'^L:\\审计提资项目资料\\', '', regex=True)
    data11['文件名'] = data11['文件路径'].str.rsplit('\\', n=1).str.get(-1)
    
    # 2. 预处理所有匹配模式
    dept_pattern = '数字应用部|数据运营部|安全运行部|平台运维部|架构与创新部|信息调度与服务部'
    
    # 清理和去重
    all_contracts = list(set([str(x) for x in list(htdf) + list(htdf2) if x and str(x) != 'nan']))
    pronum_list = list(set([str(x) for x in pronum.dropna() if x and str(x) != 'nan']))
    project_names = list(set([str(x) for x in pronameall if x and str(x) != 'nan']))
    
    # 3. 批量正则匹配
    data11['分部'] = data11['文件路径'].str.extract(f'({dept_pattern})', expand=False).fillna('')
    
    if all_contracts:
        contract_pattern = '|'.join(re.escape(c) for c in all_contracts)
        data11['合同名称'] = data11['文件路径'].str.extract(f'({contract_pattern})', expand=False).fillna('')
    else:
        data11['合同名称'] = ''
    
    if pronum_list:
        pronum_pattern = '|'.join(re.escape(p) for p in pronum_list)
        data11['项目编码'] = data11['文件路径'].str.extract(f'({pronum_pattern})', expand=False).fillna('')
    else:
        data11['项目编码'] = ''
    
    # 4. 项目名称匹配 - 超级优化
    data12 = pd.read_excel(fileDF, sheet_name='2025-06-13', engine='calamine')
    data12['文件路径'] = data12['文件路径'].str.replace(r'^L:\\审计提资项目资料\\', '', regex=True)
    data12['项目名称'] = ''
    
    # 使用最高效的匹配方式
    for project_name in project_names:
        mask = data12['文件路径'].str.contains(re.escape(project_name), na=False)
        data12.loc[mask & (data12['项目名称'] == ''), '项目名称'] = project_name
    
    df1 = data12[data12['项目名称'] != ''].copy()
    
    # 5. 快速合并
    data11['文件大小'] = pd.to_numeric(data11['文件大小'], errors='coerce').fillna(0) / 10000
    
    d5 = pd.merge(data11, df1[['文件路径', '项目名称']], on='文件路径', how='left')
    
    if not ptypeall.empty:
        d5 = pd.merge(d5, ptypeall, on='项目名称', how='left')
    else:
        d5['类型'] = ''
        d5['项目负责人'] = ''
    
    # 6. 最终处理
    d5 = d5.drop_duplicates('文件路径').fillna('')
    
    # 选择列
    cols = ['文件路径', '文件大小', '文件类型', '分部', '合同名称', '项目编码', '项目名称', '类型', '项目负责人', '文件名']
    d5 = d5[[col for col in cols if col in d5.columns]]
    
    # 7. 生成返回值
    d5_projectnames = d5['项目名称'].tolist()
    d5_filename = pd.DataFrame(d5['项目名称'].dropna().unique(), columns=['项目名称'])
    d5_filepathname = d5[['文件路径', '文件名']].values.tolist()
    df1_type = d5['类型'].tolist()
    
    # 8. 快速保存
    d5.to_excel('超级优化结果.xlsx', index=False, engine='openpyxl')
    
    total_time = (pd.Timestamp.now() - start_time).total_seconds()
    print(f"超级优化总时间: {total_time:.2f}秒")
    
    return d5, d5_filename, df1, d5_filepathname, df1_type, d5_projectnames


# 使用示例
def test_optimization():
    """测试优化效果"""
    print("=== 性能优化测试 ===")
    
    # 这里需要您提供实际的数据变量
    # data11, htdf, htdf2, pronum, pronameall, ptypeall, fileDF
    
    print("请确保以下变量已定义:")
    print("- data11: 主数据DataFrame")
    print("- htdf, htdf2: 合同数据列表")
    print("- pronum: 项目编码Series")
    print("- pronameall: 项目名称列表")
    print("- ptypeall: 项目类型DataFrame")
    print("- fileDF: Excel文件路径")
    
    # 运行优化版本
    # result = optimized_readdd(data11, htdf, htdf2, pronum, pronameall, ptypeall, fileDF)
    # 或者运行超级优化版本
    # result = super_fast_readdd(data11, htdf, htdf2, pronum, pronameall, ptypeall, fileDF)


if __name__ == "__main__":
    test_optimization()
