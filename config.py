import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# DeepSeek API配置
DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY')
DEEPSEEK_BASE_URL = "https://api.deepseek.com/v1"

# Qwen API配置 (通过OpenAI兼容接口)
QWEN_API_KEY = os.getenv('QWEN_API_KEY')
QWEN_BASE_URL = os.getenv('QWEN_BASE_URL', "https://dashscope.aliyuncs.com/compatible-mode/v1")

# 模型配置
DEFAULT_MODEL = "deepseek-chat"
QWEN_MODEL = "qwen-turbo"  # 或者 qwen-plus, qwen-max
MAX_TOKENS = 4096
TEMPERATURE = 0.7

# 批量处理配置
BATCH_SIZE = 10  # 每批处理的文本数量
DELAY_BETWEEN_BATCHES = 1  # 批次间延迟（秒） 