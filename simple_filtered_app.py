from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable
import warnings


def df_to_custom_dict(df, key_cols, value_cols=None):
    if value_cols is None:
        value_cols = [col for col in df.columns if col not in key_cols]

    result = {}
    for _, row in df.iterrows():
        key = tuple(str(row[col]) for col in key_cols)
        value = {col: row[col] for col in value_cols}
        result.setdefault(key, []).append(value)

    return result


def cal_result(df: pd.DataFrame, ffill_col: list[str]):
    gp_df = df.groupby(
        ffill_col, dropna=False
    )  # 把项目名称+项目编码合并成一个字符串做聚合

    return [
        {"key": tuple(str(k) for k in key), "records": group.to_dict(orient="records")}
        for key, group in gp_df
    ]


warnings.filterwarnings("ignore")
pd.set_option("expand_frame_repr", False)
pd.set_option("display.max_columns", None)
pd.set_option("display.max_rows", None)
pd.set_option("display.precision", 2)
pd.set_option("display.float_format", "{:,.2f}".format)

pp1 = '/Users/<USER>/Downloads/统计表-2023、2024、2025年A、B类.xlsx'
dd1 = pd.read_excel(pp1, sheet_name="2025年B类新建", skiprows=[0, 1, 2]).drop(
    columns="序号"
)
dd1[['开始年','结束年']]=dd1[['开始年','结束年']].astype('str')
dd2 = pd.read_excel(pp1, sheet_name="2024年B类新建", skiprows=[0, 1, 2]).drop(
    columns="序号"
)
dd2[['开始年','结束年']]=dd2[['开始年','结束年']].astype('str')
dd3 = pd.read_excel(pp1, sheet_name="2023年B类新建", skiprows=[0, 1, 2]).drop(
    columns="序号"
)
dd3[['开始年','结束年']]=dd3[['开始年','结束年']].astype('str')
ffill_col = [
    "项目名称",
    "项目编码",
    "建设单位",
    "二级单位",
    "建设性质",
    "管理类别",
    "项目性质",
    "开始年",
    "结束年",
    "项目总投资合计",
    "项目总投资资本性",
    "项目总投资费用性",
    "分部",
    "项目负责人"
]
dd1[ffill_col] = dd1[ffill_col].ffill()
dd2[ffill_col] = dd2[ffill_col].ffill()
dd3[ffill_col] = dd3[ffill_col].ffill()
desired_cols = [
    "标段编号",
    "标段名称",
    "标段金额合计",
    "标段金额资本性",
    "标段金额费用性",
    "合同名称",
    "合同编号",
    "合同金额",
    "中标厂家",
    "是否跨项目签合同",
    "本标段对应合同金额",
    "结算方式",
    "是否暂定价",
    "分部",
    "合同负责人",
    "已支付金额",
    "未支付金额",
    "主标段采购进度",
    "是否主标段",
    "备注",
]

# ---数据处理---
result2025 = cal_result(dd1, ffill_col)
details2025 = df_to_custom_dict(dd1, ffill_col)

result2024 = cal_result(dd2, ffill_col)
details2024 = df_to_custom_dict(dd2, ffill_col)

result2023 = cal_result(dd3, ffill_col)
details2023 = df_to_custom_dict(dd3, ffill_col)

panel_rows_2025 = [row["key"] for row in result2025]
panel_rows_2024 = [row["key"] for row in result2024]
panel_rows_2023 = [row["key"] for row in result2023]

column_frs = "200px 120px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px"

# 全局筛选状态
filter_states = {
    2025: {"project": "全部", "unit": "全部", "nature": "全部"},
    2024: {"project": "全部", "unit": "全部", "nature": "全部"},
    2023: {"project": "全部", "unit": "全部", "nature": "全部"}
}


def filter_rows(panel_rows, year):
    """根据筛选条件过滤数据"""
    state = filter_states[year]
    filtered = panel_rows
    
    if state["project"] != "全部":
        filtered = [row for row in filtered if row[0] == state["project"]]
    
    if state["unit"] != "全部":
        filtered = [row for row in filtered if row[2] == state["unit"]]
        
    if state["nature"] != "全部":
        filtered = [row for row in filtered if row[6] == state["nature"]]
    
    return filtered


@ui.page("/")
def home():
    ui.add_css(r"""
    .q-item__label {
      white-space: pre;
    }
    """)

    def ui_table_panel(panel_rows: list, year: int, details: dict):
        # 创建筛选选项
        project_options = ["全部"] + list(set([row[0] for row in panel_rows]))
        unit_options = ["全部"] + list(set([row[2] for row in panel_rows]))
        nature_options = ["全部"] + list(set([row[6] for row in panel_rows]))
        
        year_str = str(year) + "年"

        with ui.tab_panel(year_str):
            # 筛选控件容器
            filter_container = ui.column().classes("mb-4 gap-2")
            
            with filter_container:
                with ui.row().classes("items-center gap-4 flex-wrap"):
                    ui.label("筛选条件:").classes("text-sm font-medium")
                    
                    project_select = ui.select(
                        options=project_options,
                        value=filter_states[year]["project"],
                        label="项目名称"
                    ).classes("min-w-[150px]")
                    
                    unit_select = ui.select(
                        options=unit_options,
                        value=filter_states[year]["unit"],
                        label="建设单位"
                    ).classes("min-w-[150px]")
                    
                    nature_select = ui.select(
                        options=nature_options,
                        value=filter_states[year]["nature"],
                        label="项目性质"
                    ).classes("min-w-[120px]")
                    
                    def reset_filters():
                        filter_states[year]["project"] = "全部"
                        filter_states[year]["unit"] = "全部"
                        filter_states[year]["nature"] = "全部"
                        project_select.value = "全部"
                        unit_select.value = "全部"
                        nature_select.value = "全部"
                        update_display()
                    
                    ui.button("重置", on_click=reset_filters).props("size=sm outline")
            
            # 数据显示容器
            data_container = ui.column().classes("gap-0 w-full items-stretch")
            
            def update_display():
                # 更新筛选状态
                filter_states[year]["project"] = project_select.value
                filter_states[year]["unit"] = unit_select.value
                filter_states[year]["nature"] = nature_select.value
                
                # 过滤数据
                filtered_data = filter_rows(panel_rows, year)
                
                # 清空并重新创建显示内容
                data_container.clear()
                
                with data_container:
                    ui.label(f"共 {len(filtered_data)} 个项目").classes("text-sm text-gray-600 mb-2")
                    
                    # 分页处理
                    page_size = 20
                    total_pages = (len(filtered_data) + page_size - 1) // page_size
                    current_page = 0
                    
                    def show_page(page_num):
                        start_idx = page_num * page_size
                        end_idx = min(start_idx + page_size, len(filtered_data))
                        page_data = filtered_data[start_idx:end_idx]
                        
                        # 表头
                        with ui.grid(columns=column_frs).classes("place-items-center px-[16px] gap-2"):
                            for col in ffill_col:
                                ui.label(col)
                        
                        # 数据行
                        for row in page_data:
                            with ui.expansion().classes("border-b-2 border-gray-300 w-[2000px]").props("dense") as expan_item:
                                with expan_item.add_slot("header"):
                                    with ui.grid(columns=column_frs).classes("gap-2 items-center"):
                                        for j in row:
                                            ui.badge(j)
                                
                                ui.table(
                                    rows=details[row],
                                    columns=[
                                        {"name": "name", "field": col, "label": col}
                                        for col in desired_cols
                                        if col in dd1.columns
                                    ],
                                )
                    
                    show_page(current_page)
            
            # 绑定筛选事件
            project_select.on_value_change(lambda: update_display())
            unit_select.on_value_change(lambda: update_display())
            nature_select.on_value_change(lambda: update_display())
            
            # 初始显示
            update_display()

    with ui.tabs() as tabs:
        ui.tab("2023年")
        ui.tab("2024年")
        ui.tab("2025年")

    with ui.tab_panels(tabs, value="2025年").classes("w-full"):
        ui_table_panel(panel_rows_2023, 2023, details2023)
        ui_table_panel(panel_rows_2024, 2024, details2024)
        ui_table_panel(panel_rows_2025, 2025, details2025)


ui.run()
