import pandas as pd

# 读取数据
dd1 = pd.read_excel(pp1, sheet_name='2025年B类新建', skiprows=[0,1,2])

# 填充指定列
ffill_col = ['项目名称','项目编码','建设单位','二级单位','建设性质','管理类别','项目性质','开始年','结束年','合计','资本性','费用性']
dd1[ffill_col] = dd1[ffill_col].ffill()

# 获取项目名称和编码
dd1项目名称 = dd1[['项目名称','项目编码']].drop_duplicates('项目名称').dropna().values.tolist()
ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]
data = [i for i in ll1项目名称]

# 指定需要的列
desired_cols = [
    "标段名称", "标段编号", "合同名称", "合同编号",
    "合同金额", "中标厂家", '是否跨项目签合同', '本标段对应合同金额'
]

# 获取dd1中实际存在的列（最多3个）
available_cols = [col for col in desired_cols if col in dd1.columns][:3]
print(f"可用的列: {available_cols}")

# 方法1: 为每个项目使用dd1中对应的第一行数据
details_method1 = {}
for row_key in data:
    # 提取项目名称
    project_name = row_key.split('     ')[0]
    
    # 在dd1中找到匹配的行
    matching_rows = dd1[dd1['项目名称'] == project_name]
    
    if not matching_rows.empty:
        # 使用第一个匹配的行
        first_row = matching_rows.iloc[0]
        
        details_method1[row_key] = {
            "rows": [
                {
                    "name": available_cols[j] if j < len(available_cols) else f"字段{j+1}",
                    "value": str(first_row[available_cols[j]]) if j < len(available_cols) and pd.notna(first_row[available_cols[j]]) else ""
                }
                for j in range(3)
            ]
        }
    else:
        # 如果没找到匹配行，使用空值
        details_method1[row_key] = {
            "rows": [
                {"name": available_cols[j] if j < len(available_cols) else f"字段{j+1}", "value": ""}
                for j in range(3)
            ]
        }

print("方法1结果（每个项目使用第一行数据）:")
for key, value in list(details_method1.items())[:2]:
    print(f"{key}: {value}")

# 方法2: 为每个项目使用dd1中所有匹配行的数据（创建多个details条目）
details_method2 = {}
for row_key in data:
    project_name = row_key.split('     ')[0]
    matching_rows = dd1[dd1['项目名称'] == project_name]
    
    if not matching_rows.empty:
        # 为每个匹配行创建一个条目
        for idx, (_, row_data) in enumerate(matching_rows.iterrows()):
            # 如果有多行，在key后面加上序号
            key_suffix = f"_{idx+1}" if len(matching_rows) > 1 else ""
            detail_key = f"{row_key}{key_suffix}"
            
            details_method2[detail_key] = {
                "rows": [
                    {
                        "name": available_cols[j] if j < len(available_cols) else f"字段{j+1}",
                        "value": str(row_data[available_cols[j]]) if j < len(available_cols) and pd.notna(row_data[available_cols[j]]) else ""
                    }
                    for j in range(3)
                ]
            }
    else:
        details_method2[row_key] = {
            "rows": [
                {"name": available_cols[j] if j < len(available_cols) else f"字段{j+1}", "value": ""}
                for j in range(3)
            ]
        }

print("\n方法2结果（每个匹配行都创建条目）:")
for key, value in list(details_method2.items())[:3]:
    print(f"{key}: {value}")

# 方法3: 直接使用dd1的行索引（推荐用于完整数据）
details_method3 = {}
for i, row_key in enumerate(data):
    if i < len(dd1):
        # 直接使用dd1的第i行数据
        dd1_row = dd1.iloc[i]
        
        details_method3[row_key] = {
            "rows": [
                {
                    "name": available_cols[j] if j < len(available_cols) else f"字段{j+1}",
                    "value": str(dd1_row[available_cols[j]]) if j < len(available_cols) and pd.notna(dd1_row[available_cols[j]]) else ""
                }
                for j in range(3)
            ]
        }
    else:
        details_method3[row_key] = {
            "rows": [
                {"name": available_cols[j] if j < len(available_cols) else f"字段{j+1}", "value": ""}
                for j in range(3)
            ]
        }

print("\n方法3结果（按索引顺序使用dd1行）:")
for key, value in list(details_method3.items())[:2]:
    print(f"{key}: {value}")

# 最终推荐版本（根据您的需求选择）
details = details_method1  # 或者选择其他方法

print(f"\n最终details包含 {len(details)} 个项目")
print("最终结果示例:")
for key, value in list(details.items())[:2]:
    print(f"{key}: {value}")
