#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Qwen3.0文件名处理器
使用OpenAI库调用Qwen3.0 API进行文件名的关键词匹配和删除
"""

import time
import json
import logging
import re
from typing import List, Dict, Any, Optional, Tuple
from openai import OpenAI
from tqdm import tqdm
import pandas as pd
from config import (
    QWEN_API_KEY, 
    QWEN_BASE_URL, 
    QWEN_MODEL, 
    MAX_TOKENS, 
    TEMPERATURE,
    BATCH_SIZE,
    DELAY_BETWEEN_BATCHES
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class QwenFileProcessor:
    """Qwen3.0文件名处理器"""
    
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        """
        初始化Qwen处理器
        
        Args:
            api_key: Qwen API密钥
            base_url: API基础URL
        """
        self.api_key = api_key or QWEN_API_KEY
        self.base_url = base_url or QWEN_BASE_URL
        
        if not self.api_key:
            raise ValueError("Qwen API密钥未设置，请在.env文件中设置QWEN_API_KEY")
        
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
        
        logger.info("Qwen文件处理器初始化完成")
    
    def create_matching_prompt(self, keywords: List[str], filename: str) -> str:
        """
        创建用于匹配判断的提示词
        
        Args:
            keywords: 关键词列表
            filename: 文件名
            
        Returns:
            提示词字符串
        """
        keywords_str = "、".join(keywords)
        
        prompt = f"""你是一个文件名分析专家。请分析以下文件名中是否包含指定的关键词。

关键词列表：{keywords_str}

文件名：{filename}

请按照以下要求进行分析：
1. 判断文件名中是否包含关键词列表中的任何一个关键词（支持模糊匹配，包括部分匹配、同义词、相关词汇）
2. 如果包含，请识别出所有匹配的关键词
3. 请返回JSON格式的结果

返回格式：
{{
    "has_match": true/false,
    "matched_keywords": ["匹配的关键词1", "匹配的关键词2"],
    "remaining_filename": "删除匹配关键词后的文件名",
    "explanation": "匹配原因说明"
}}

注意：
- 模糊匹配包括：完全匹配、部分匹配、同义词匹配、相关概念匹配
- 删除关键词时要保持文件名的可读性
- 如果没有匹配，remaining_filename应该是原文件名
"""
        return prompt
    
    def process_single_filename(self, keywords: List[str], filename: str, model: str = QWEN_MODEL) -> Dict[str, Any]:
        """
        处理单个文件名
        
        Args:
            keywords: 关键词列表
            filename: 文件名
            model: 使用的模型名称
            
        Returns:
            处理结果字典
        """
        try:
            prompt = self.create_matching_prompt(keywords, filename)
            
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": prompt}
                ],
                max_tokens=MAX_TOKENS,
                temperature=TEMPERATURE
            )
            
            response_content = response.choices[0].message.content
            
            # 尝试解析JSON响应
            try:
                result_data = json.loads(response_content)
            except json.JSONDecodeError:
                # 如果JSON解析失败，尝试提取信息
                logger.warning(f"JSON解析失败，原始响应: {response_content}")
                result_data = self._extract_info_from_text(response_content, filename)
            
            result = {
                "original_filename": filename,
                "keywords": keywords,
                "has_match": result_data.get("has_match", False),
                "matched_keywords": result_data.get("matched_keywords", []),
                "remaining_filename": result_data.get("remaining_filename", filename),
                "explanation": result_data.get("explanation", ""),
                "raw_response": response_content,
                "model": model,
                "usage": response.usage.dict() if response.usage else None,
                "status": "success"
            }
            
            return result
            
        except Exception as e:
            logger.error(f"处理文件名时出错: {str(e)}")
            return {
                "original_filename": filename,
                "keywords": keywords,
                "has_match": False,
                "matched_keywords": [],
                "remaining_filename": filename,
                "explanation": "",
                "error": str(e),
                "status": "error"
            }
    
    def _extract_info_from_text(self, text: str, original_filename: str) -> Dict[str, Any]:
        """
        从文本中提取信息（当JSON解析失败时使用）
        
        Args:
            text: 响应文本
            original_filename: 原始文件名
            
        Returns:
            提取的信息字典
        """
        # 简单的文本解析逻辑
        has_match = "true" in text.lower() or "匹配" in text or "包含" in text
        
        return {
            "has_match": has_match,
            "matched_keywords": [],
            "remaining_filename": original_filename,
            "explanation": text
        }
    
    def process_filename_batch(self, keywords: List[str], filenames: List[str], model: str = QWEN_MODEL) -> List[Dict[str, Any]]:
        """
        批量处理文件名
        
        Args:
            keywords: 关键词列表
            filenames: 文件名列表
            model: 使用的模型名称
            
        Returns:
            处理结果列表
        """
        results = []
        
        logger.info(f"开始处理 {len(filenames)} 个文件名，关键词数量: {len(keywords)}")
        
        for filename in tqdm(filenames, desc="处理文件名"):
            result = self.process_single_filename(keywords, filename, model)
            results.append(result)
            
            # 添加延迟避免API限制
            time.sleep(0.1)
        
        return results
    
    def process_large_filename_batch(self, keywords: List[str], filenames: List[str], model: str = QWEN_MODEL) -> List[Dict[str, Any]]:
        """
        处理大批量文件名，分批处理
        
        Args:
            keywords: 关键词列表
            filenames: 文件名列表
            model: 使用的模型名称
            
        Returns:
            处理结果列表
        """
        all_results = []
        total_batches = (len(filenames) + BATCH_SIZE - 1) // BATCH_SIZE
        
        logger.info(f"开始批量处理 {len(filenames)} 个文件名，共 {total_batches} 批")
        
        for i in range(0, len(filenames), BATCH_SIZE):
            batch_filenames = filenames[i:i + BATCH_SIZE]
            batch_num = i // BATCH_SIZE + 1
            
            logger.info(f"处理第 {batch_num}/{total_batches} 批，包含 {len(batch_filenames)} 个文件名")
            
            batch_results = self.process_filename_batch(keywords, batch_filenames, model)
            all_results.extend(batch_results)
            
            # 批次间延迟
            if i + BATCH_SIZE < len(filenames):
                time.sleep(DELAY_BETWEEN_BATCHES)
        
        logger.info(f"批量处理完成，共处理 {len(all_results)} 个文件名")
        return all_results
    
    def save_results(self, results: List[Dict[str, Any]], output_file: str):
        """
        保存处理结果到文件
        
        Args:
            results: 处理结果列表
            output_file: 输出文件路径
        """
        try:
            # 创建简化的结果用于CSV
            simplified_results = []
            for result in results:
                simplified_results.append({
                    "原始文件名": result["original_filename"],
                    "是否匹配": result["has_match"],
                    "匹配的关键词": ", ".join(result["matched_keywords"]),
                    "处理后文件名": result["remaining_filename"],
                    "说明": result["explanation"],
                    "状态": result["status"]
                })
            
            # 保存为CSV
            df = pd.DataFrame(simplified_results)
            csv_file = output_file.replace('.json', '.csv')
            df.to_csv(csv_file, index=False, encoding='utf-8-sig')  # 使用utf-8-sig确保Excel正确显示中文
            logger.info(f"结果已保存到CSV文件: {csv_file}")
            
            # 保存完整结果为JSON
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"完整结果已保存到JSON文件: {output_file}")
            
        except Exception as e:
            logger.error(f"保存结果时出错: {str(e)}")
    
    def get_processing_stats(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        Args:
            results: 处理结果列表
            
        Returns:
            统计信息字典
        """
        total = len(results)
        successful = sum(1 for r in results if r.get('status') == 'success')
        failed = total - successful
        matched = sum(1 for r in results if r.get('has_match', False))
        
        total_tokens = 0
        for result in results:
            if result.get('usage'):
                total_tokens += result['usage'].get('total_tokens', 0)
        
        stats = {
            "total_files": total,
            "successful": successful,
            "failed": failed,
            "success_rate": successful / total if total > 0 else 0,
            "matched_files": matched,
            "match_rate": matched / successful if successful > 0 else 0,
            "total_tokens": total_tokens
        }
        
        return stats
    
    def extract_remaining_filenames(self, results: List[Dict[str, Any]]) -> List[str]:
        """
        提取所有处理后的文件名
        
        Args:
            results: 处理结果列表
            
        Returns:
            处理后的文件名列表
        """
        remaining_filenames = []
        for result in results:
            if result.get('status') == 'success':
                remaining_filenames.append(result.get('remaining_filename', result.get('original_filename')))
            else:
                remaining_filenames.append(result.get('original_filename'))
        
        return remaining_filenames
