from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable


def load_project_data(file_path: str = '/Users/<USER>/Downloads/统计表(1).xlsx', 
                     sheet_name: str = '2023年B类新建') -> list:
    """
    加载项目数据
    
    Args:
        file_path: Excel文件路径
        sheet_name: 工作表名称
    
    Returns:
        项目数据列表
    """
    try:
        dd1 = pd.read_excel(file_path, sheet_name=sheet_name, skiprows=[0, 2, 3])
        dd1项目名称 = dd1[['项目名称', '项目编码']].dropna().values.tolist()
        ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]
        data = [i for i in ll1项目名称]
        return data
    except Exception as e:
        print(f"加载数据失败: {e}")
        return []


def create_project_details(data: list) -> dict:
    """
    创建项目详情数据
    
    Args:
        data: 项目数据列表
    
    Returns:
        项目详情字典
    """
    details = {
        row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
        for row in data
    }
    return details


def get_project_data_and_details(file_path: str = '/Users/<USER>/Downloads/统计表(1).xlsx', 
                                sheet_name: str = '2023年B类新建') -> tuple:
    """
    一次性获取项目数据和详情
    
    Args:
        file_path: Excel文件路径
        sheet_name: 工作表名称
    
    Returns:
        (data, details) 元组
    """
    data = load_project_data(file_path, sheet_name)
    details = create_project_details(data)
    return data, details


@ui.page("/")
def home():
    # 方式1: 分别调用函数
    # data = load_project_data()
    # details = create_project_details(data)
    
    # 方式2: 一次性获取（推荐）
    data, details = get_project_data_and_details()
    
    print(f"加载了 {len(data)} 个项目")
    print("详情数据示例:", list(details.keys())[:3] if details else "无数据")
    
    if not data:
        ui.label("没有加载到数据，请检查文件路径和格式").classes("text-red-500")
        return
    
    summary_data = rxui.use_pagination(data, page_size=100)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=10")

        with ui.column().classes("gap-0 w-full items-stretch"):
            for row in summary_data.current_source.value:
                with ui.expansion(text=row).classes(
                    "border-b-2 border-gray-300"
                ).props("dense"):
                    ui.table(
                        rows=details[row]["rows"],
                        columns=[
                            {"name": "name", "field": "name", "label": "标段名称"},
                            {"name": "value", "field": "value", "label": "项目总投资"},
                        ],
                    )


if __name__ == "__main__":
    ui.run()
