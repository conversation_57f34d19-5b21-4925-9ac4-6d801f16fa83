import pandas as pd

# 假设pp1是您的Excel文件路径
# pp1 = 'your_file_path.xlsx'

# 读取数据
dd1 = pd.read_excel(pp1, sheet_name='2025年B类新建', skiprows=[0,1,2])
print(dd1.head(5))

# 获取项目名称和编码
dd1项目名称 = dd1[['项目名称','项目编码']].dropna().values.tolist()
print(dd1项目名称)

# 创建项目名称列表
ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]
data = [i for i in ll1项目名称]

# 方法1: 使用dd1的所有列作为rows
details_method1 = {}
for row_name in data:
    # 找到对应的原始数据行
    original_row_index = data.index(row_name)
    original_row_data = dd1项目名称[original_row_index]
    
    details_method1[row_name] = {
        "rows": [
            {"name": f"项目名称", "value": str(original_row_data[0])},
            {"name": f"项目编码", "value": str(original_row_data[1])}
        ]
    }

print("方法1 - 使用项目名称和编码:")
for key, value in list(details_method1.items())[:3]:  # 显示前3个
    print(f"{key}: {value}")

# 方法2: 使用dd1的更多列（如果存在）
details_method2 = {}
for row_name in data:
    original_row_index = data.index(row_name)
    # 获取dd1中对应行的所有数据
    full_row_data = dd1.iloc[original_row_index].dropna().tolist()
    
    details_method2[row_name] = {
        "rows": [
            {"name": f"字段{j+1}", "value": str(full_row_data[j]) if j < len(full_row_data) else ""}
            for j in range(len(full_row_data))
        ]
    }

print("\n方法2 - 使用dd1的所有列:")
for key, value in list(details_method2.items())[:2]:  # 显示前2个
    print(f"{key}: {value}")

# 方法3: 使用dd1的特定列，并用列名作为name
details_method3 = {}
for row_name in data:
    original_row_index = data.index(row_name)
    
    rows_data = []
    for col_name in dd1.columns:
        cell_value = dd1.iloc[original_row_index][col_name]
        if pd.notna(cell_value):  # 只添加非空值
            rows_data.append({
                "name": str(col_name),
                "value": str(cell_value)
            })
    
    details_method3[row_name] = {"rows": rows_data}

print("\n方法3 - 使用列名作为name:")
for key, value in list(details_method3.items())[:2]:  # 显示前2个
    print(f"{key}: {value}")

# 方法4: 如果您想要固定3个字段，可以指定具体的列
details_method4 = {}
target_columns = ['项目名称', '项目编码']  # 您可以根据需要修改这些列名

# 如果dd1有第三列，添加它
if len(dd1.columns) > 2:
    target_columns.append(dd1.columns[2])

for row_name in data:
    original_row_index = data.index(row_name)
    
    rows_data = []
    for j, col_name in enumerate(target_columns):
        if col_name in dd1.columns:
            cell_value = dd1.iloc[original_row_index][col_name]
            rows_data.append({
                "name": str(col_name),
                "value": str(cell_value) if pd.notna(cell_value) else ""
            })
        else:
            rows_data.append({
                "name": f"字段{j+1}",
                "value": ""
            })
    
    details_method4[row_name] = {"rows": rows_data}

print("\n方法4 - 指定列名:")
for key, value in list(details_method4.items())[:2]:  # 显示前2个
    print(f"{key}: {value}")

# 最终推荐的版本（根据您的需求选择）
# 如果您想要所有非空列：
details = {}
for row_name in data:
    original_row_index = data.index(row_name)
    
    # 获取该行的所有非空数据
    row_data = dd1.iloc[original_row_index]
    non_null_data = [(col, val) for col, val in row_data.items() if pd.notna(val)]
    
    details[row_name] = {
        "rows": [
            {"name": str(col), "value": str(val)}
            for col, val in non_null_data
        ]
    }

print("\n最终版本 - 所有非空列:")
for key, value in list(details.items())[:2]:  # 显示前2个
    print(f"{key}: {value}")
