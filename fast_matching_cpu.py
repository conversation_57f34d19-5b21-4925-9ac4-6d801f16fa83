import os
import warnings
import time
import re
import pandas as pd
import numpy as np
from collections import defaultdict
from functools import lru_cache
from numba import njit
from concurrent.futures import ThreadPoolExecutor
import multiprocessing as mp
from typing import List, Tuple, Dict
import gc

warnings.filterwarnings('ignore')

class FastFileMatching:
    def __init__(self):
        self.start_time = time.time()
        
        # 文件路径配置 - 根据您的原始代码
        self.p1 = '/Users/<USER>/Desktop/2025年项目资料/信息化项目里程碑及交付物标准 20240801_v4.xlsx'
        self.fileDF = '/Users/<USER>/Desktop/2025年项目资料/file0616.xlsx'
        self.profile = '/Users/<USER>/Documents/近三年投资计划/2018年至今项目清单（信息化项目）.xlsx'
        self.htp = '/Users/<USER>/Documents/科室项目管理周报/合同导数数据/合同查询导出_20250610143721870.xlsx'
        
        # 预编译正则表达式
        self.punctuation_pattern = re.compile(r'[，。、；：""''（）《》【】—…·～！？　 \n\r\t()\[\]{}!@#$%^&*\-_=+|\\:;"\'<>,.?0-9`~·！￥…（）《》【】——]+')
        
    def log_time(self, step_name: str):
        """记录执行时间"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        print(f"{step_name}: {elapsed:.2f} 秒")
        self.start_time = current_time

    @lru_cache(maxsize=2000)
    def clean_text(self, text: str) -> str:
        """缓存版本的文本清理"""
        if not text:
            return ""
        return self.punctuation_pattern.sub('', text)

    def optimize_dataframe(self, df: pd.DataFrame) -> pd.DataFrame:
        """优化DataFrame内存使用"""
        # 先处理数值类型
        for col in df.select_dtypes(include=['int64']):
            df[col] = pd.to_numeric(df[col], downcast='integer')

        for col in df.select_dtypes(include=['float64']):
            df[col] = pd.to_numeric(df[col], downcast='float')

        # 暂时不转换为category类型，避免后续赋值问题
        # 如果需要节省内存，可以在数据处理完成后再转换

        return df

    def load_all_data(self):
        """一次性加载所有数据"""
        print("开始加载数据...")
        
        # 1. 加载标准数据
        try:
            s1 = pd.read_excel(self.p1, sheet_name='1_信息系统建设与升级改造', skiprows=[0], engine='calamine')['交付物名称'].dropna().tolist()
            s2 = pd.read_excel(self.p1, sheet_name='4_运维维护', skiprows=[0], engine='calamine')['交付物名称'].dropna().tolist()
            s5 = pd.read_excel(self.p1, sheet_name='5_信息专题研究项目', skiprows=[0], engine='calamine')['交付物名称'].dropna().tolist()
            
            type2biaozhun = {
                '信息化': s1,
                '信息维护修理': s2,
                '数据资产维护': s2,
                '专题研究': s5
            }
        except Exception as e:
            print(f"加载标准数据失败: {e}")
            type2biaozhun = {}
        
        self.log_time("标准数据加载")
        
        # 2. 加载项目数据
        try:
            # 历年投资计划
            yearss = ['2022年年初', '2022年年中', '2023年年初', '2023年年中', '2024年年初', '2024年年中', '2025年年初']
            ptype1 = pd.read_excel(self.profile, sheet_name='历年投资计划', engine='calamine')
            ptype1 = ptype1[ptype1['投资年份'].isin(yearss)][['项目名称', '项目编码', '类型', '分部', '项目负责人']]
            
            # 历年信息维护修理
            ptype2 = pd.read_excel(self.profile, sheet_name='历年信息维护修理', engine='calamine')[['合同名称', '合同编号', '类型', '分部', '项目负责人']]
            ptype2 = ptype2.rename(columns={'合同名称': '项目名称', '合同编号': '项目编码'}).dropna(subset=['项目名称'])
            
            ptypeall = pd.concat([ptype1, ptype2], axis=0, ignore_index=True)
            ptypeall = self.optimize_dataframe(ptypeall)
            
            # 提取项目名称列表
            pronameall = ptypeall['项目名称'].dropna().tolist()
            
        except Exception as e:
            print(f"加载项目数据失败: {e}")
            ptypeall = pd.DataFrame()
            pronameall = []
        
        self.log_time("项目数据加载")
        
        # 3. 加载合同数据
        try:
            htdf = pd.read_excel(self.htp, engine='calamine')['合同编号'].dropna().tolist()
            htdf2 = ptype2['项目编码'].dropna().tolist() if 'ptype2' in locals() else []
        except Exception as e:
            print(f"加载合同数据失败: {e}")
            htdf, htdf2 = [], []
        
        # 4. 加载主文件数据
        try:
            data00 = pd.read_excel(self.fileDF, sheet_name='2025-06-13', engine='calamine')
            data00 = self.optimize_dataframe(data00)
        except Exception as e:
            print(f"加载主文件失败: {e}")
            data00 = pd.DataFrame()
        
        self.log_time("主文件数据加载")
        
        return type2biaozhun, ptypeall, pronameall, htdf, htdf2, data00

    @njit
    def fast_lcs_length(self, a, b):
        """快速LCS长度计算"""
        m, n = len(a), len(b)
        if m == 0 or n == 0:
            return 0
        
        # 使用一维数组优化空间
        prev = np.zeros(n + 1, dtype=np.int32)
        
        for i in range(m):
            curr = np.zeros(n + 1, dtype=np.int32)
            for j in range(n):
                if a[i] == b[j]:
                    curr[j + 1] = prev[j] + 1
                else:
                    curr[j + 1] = max(prev[j + 1], curr[j])
            prev = curr
        
        return prev[n]

    def calculate_match_score(self, text1: str, text2: str) -> int:
        """计算匹配分数"""
        if not text1 or not text2:
            return 0
        
        try:
            # 转换为numpy数组进行快速比较
            a_bytes = np.frombuffer(text1.encode('utf-8'), dtype=np.uint8)
            b_bytes = np.frombuffer(text2.encode('utf-8'), dtype=np.uint8)
            return self.fast_lcs_length(a_bytes, b_bytes)
        except:
            return 0

    def process_single_file(self, args):
        """处理单个文件的匹配"""
        file_info, standards_cleaned, standards_raw = args
        project_name, file_name = file_info
        
        # 快速文本分割
        split_marks = ["-", " ", "_", "、", "：", "项目", "电网", "（", "）", "(", ")", "盖章"]
        text_parts = [file_name]
        
        for mark in split_marks:
            new_parts = []
            for part in text_parts:
                if mark in part:
                    new_parts.extend(part.split(mark))
                else:
                    new_parts.append(part)
            text_parts = new_parts
        
        best_match = None
        best_score = 0
        best_standard_idx = -1
        
        for part in text_parts:
            part_clean = self.clean_text(part)
            if len(part_clean) < 2:
                continue
            
            for idx, standard_clean in enumerate(standards_cleaned):
                if len(standard_clean) < 2:
                    continue
                
                score = self.calculate_match_score(standard_clean, part_clean)
                if score > best_score:
                    best_score = score
                    best_standard_idx = idx
                    best_match = part_clean
        
        if best_score > 1:  # 最小匹配阈值
            return (standards_raw[best_standard_idx], (file_name, project_name), best_score)
        
        return None

    def batch_match_files(self, standards: List[str], file_list: List[Tuple], max_workers: int = None):
        """批量匹配文件"""
        if not max_workers:
            max_workers = min(mp.cpu_count(), 6)
        
        # 预处理标准列表
        standards_cleaned = [self.clean_text(std) for std in standards]
        
        # 准备任务
        tasks = [(file_info, standards_cleaned, standards) for file_info in file_list]
        
        results = defaultdict(list)
        no_matches = []
        
        # 使用线程池处理
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            match_results = list(executor.map(self.process_single_file, tasks))
        
        for file_info, result in zip(file_list, match_results):
            if result:
                standard, match_pair, score = result
                results[standard].append(match_pair)
            else:
                no_matches.append(file_info)
        
        return dict(results), no_matches

    def process_main_data(self, data00, ptypeall, pronameall, htdf, htdf2):
        """处理主数据"""
        print("开始处理主数据...")

        # 确保数据是可修改的副本
        data00 = data00.copy()

        # 确保关键列是字符串类型，避免category类型的赋值问题
        string_columns = ['文件路径', '文件名', '分部', '合同名称', '项目编码', '项目名称']
        for col in string_columns:
            if col in data00.columns:
                if data00[col].dtype.name == 'category':
                    data00[col] = data00[col].astype('object')

        # 预处理文件路径
        data00['文件路径'] = data00['文件路径'].astype(str).str.replace(r'^L:\\审计提资项目资料\\', '', regex=True)
        data00['文件名'] = data00['文件路径'].str.rsplit('\\', n=1).str.get(-1)

        # 批量正则匹配
        dept_list = ['数字应用部', '数据运营部', '安全运行部', '平台运维部', '架构与创新部', '信息调度与服务部']
        dept_pattern = '|'.join(dept_list)
        data00['分部'] = data00['文件路径'].str.extract(f'({dept_pattern})', expand=False).fillna('')

        # 合同匹配
        if htdf or htdf2:
            contract_pattern = '|'.join(str(x) for x in htdf + htdf2 if x)
            if contract_pattern:
                data00['合同名称'] = data00['文件路径'].str.extract(f'({contract_pattern})', expand=False).fillna('')
            else:
                data00['合同名称'] = ''
        else:
            data00['合同名称'] = ''

        # 项目编码匹配
        if not ptypeall.empty and '项目编码' in ptypeall.columns:
            project_codes = ptypeall['项目编码'].dropna().astype(str).tolist()
            project_codes = [code for code in project_codes if code and code != 'nan']
            if project_codes:
                code_pattern = '|'.join(re.escape(str(code)) for code in project_codes)
                data00['项目编码'] = data00['文件路径'].str.extract(f'({code_pattern})', expand=False).fillna('')
            else:
                data00['项目编码'] = ''
        else:
            data00['项目编码'] = ''

        # 项目名称匹配 - 优化版本
        data00['项目名称'] = ''
        for project_name in pronameall:
            if not project_name or pd.isna(project_name):
                continue
            try:
                mask = data00['文件路径'].str.contains(re.escape(str(project_name)), na=False)
                empty_mask = (data00['项目名称'] == '') | data00['项目名称'].isna()
                data00.loc[mask & empty_mask, '项目名称'] = str(project_name)
            except Exception as e:
                print(f"处理项目名称 '{project_name}' 时出错: {e}")
                continue
        
        # 合并项目信息
        if not ptypeall.empty and '项目名称' in ptypeall.columns:
            # 确保ptypeall中的字符串列也不是category类型
            ptypeall_copy = ptypeall.copy()
            for col in ptypeall_copy.select_dtypes(include=['category']):
                ptypeall_copy[col] = ptypeall_copy[col].astype('object')

            try:
                result_data = pd.merge(data00, ptypeall_copy, on='项目名称', how='left', suffixes=('', '_proj'))
            except Exception as e:
                print(f"合并项目信息时出错: {e}")
                result_data = data00.copy()
                result_data['类型'] = ''
                result_data['项目负责人'] = ''
        else:
            result_data = data00.copy()
            result_data['类型'] = ''
            result_data['项目负责人'] = ''

        # 清理数据
        result_data = result_data.drop_duplicates('文件路径')

        # 安全处理文件大小
        if '文件大小' in result_data.columns:
            try:
                result_data['文件大小'] = pd.to_numeric(result_data['文件大小'], errors='coerce').fillna(0) / 10000
            except Exception as e:
                print(f"处理文件大小时出错: {e}")
                result_data['文件大小'] = 0

        # 选择最终列
        final_cols = ['文件路径', '文件大小', '文件类型', '分部', '合同名称', '项目编码', '项目名称', '类型', '项目负责人', '文件名']
        available_cols = [col for col in final_cols if col in result_data.columns]
        result_data = result_data[available_cols].fillna('')

        self.log_time("主数据处理完成")
        return result_data

    def run_optimized_matching(self):
        """运行优化的匹配流程"""
        print("=== 启动快速文件匹配系统 ===")
        total_start = time.time()
        
        # 1. 加载所有数据
        type2biaozhun, ptypeall, pronameall, htdf, htdf2, data00 = self.load_all_data()
        
        if data00.empty:
            print("主数据为空，退出程序")
            return None
        
        # 2. 处理主数据
        processed_data = self.process_main_data(data00, ptypeall, pronameall, htdf, htdf2)
        
        # 3. 执行匹配
        print("开始执行文件匹配...")
        all_matches = {}
        all_no_matches = []
        
        # 按类型分组处理
        for match_type, standards in type2biaozhun.items():
            type_data = processed_data[processed_data['类型'] == match_type]
            if type_data.empty:
                continue
            
            print(f"处理类型 '{match_type}': {len(type_data)} 个文件")
            
            file_list = [(row['项目名称'], row['文件名']) for _, row in type_data.iterrows()]
            matches, no_matches = self.batch_match_files(standards, file_list)
            
            # 合并结果
            for standard, match_list in matches.items():
                if standard not in all_matches:
                    all_matches[standard] = []
                all_matches[standard].extend(match_list)
            
            all_no_matches.extend(no_matches)
        
        self.log_time("匹配执行完成")
        
        # 4. 生成结果
        print("生成最终结果...")
        match_records = []
        for standard, matches in all_matches.items():
            for filename, project in matches:
                match_records.append({
                    '交付物标准': standard,
                    '文件名': filename,
                    '项目名称': project
                })
        
        # 5. 保存结果到Excel
        self.save_results_to_excel(processed_data, match_records, all_no_matches, type2biaozhun)
        
        # 6. 统计信息
        total_time = time.time() - total_start
        print(f"\n=== 执行统计 ===")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"处理文件: {len(processed_data)} 个")
        print(f"匹配成功: {len(match_records)} 个")
        print(f"未匹配: {len(all_no_matches)} 个")
        
        success_rate = len(match_records) / (len(match_records) + len(all_no_matches)) * 100 if (len(match_records) + len(all_no_matches)) > 0 else 0
        print(f"匹配成功率: {success_rate:.1f}%")
        
        return processed_data, match_records

    def save_results_to_excel(self, processed_data, match_records, no_matches, type2biaozhun):
        """简化版保存结果到Excel"""
        print("正在保存结果到Excel...")

        try:
            # 1. 匹配结果DataFrame
            if match_records:
                match_df = pd.DataFrame(match_records)
            else:
                match_df = pd.DataFrame()

            # 2. 未匹配文件DataFrame
            if no_matches:
                nomatch_df = pd.DataFrame(no_matches, columns=['项目名称', '文件名'])
            else:
                nomatch_df = pd.DataFrame()

            # 3. 保存到Excel
            output_file = '匹配结果.xlsx'
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 保存匹配结果
                if not match_df.empty:
                    match_df.to_excel(writer, sheet_name='匹配结果', index=False)

                # 保存处理后数据
                processed_data.to_excel(writer, sheet_name='处理数据', index=False)

                # 保存未匹配文件
                if not nomatch_df.empty:
                    nomatch_df.to_excel(writer, sheet_name='未匹配', index=False)

            print(f"✅ 结果已保存到: {output_file}")

        except Exception as e:
            print(f"❌ 保存Excel失败: {e}")
            # 备选：保存为CSV
            try:
                if match_records:
                    pd.DataFrame(match_records).to_csv('匹配结果.csv', index=False, encoding='utf-8-sig')
                processed_data.to_csv('处理数据.csv', index=False, encoding='utf-8-sig')
                print("✅ 已保存为CSV格式")
            except Exception as csv_error:
                print(f"❌ CSV保存也失败: {csv_error}")


def main():
    """主函数"""
    matcher = FastFileMatching()
    try:
        result = matcher.run_optimized_matching()
        print("匹配完成!")
        return result
    except Exception as e:
        print(f"执行错误: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    result = main()
    gc.collect()  # 清理内存
