#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名关键词处理主程序
使用Qwen3.0进行文件名的关键词匹配和删除
"""

import os
import sys
import json
from typing import List, Dict, Any
from qwen_file_processor import QwenFileProcessor


def load_list_from_file(file_path: str) -> List[str]:
    """
    从文件加载列表
    
    Args:
        file_path: 文件路径
        
    Returns:
        字符串列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            items = [line.strip() for line in f if line.strip()]
        return items
    except Exception as e:
        print(f"读取文件 {file_path} 时出错: {str(e)}")
        return []


def save_list_to_file(items: List[str], file_path: str):
    """
    保存列表到文件
    
    Args:
        items: 字符串列表
        file_path: 文件路径
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            for item in items:
                f.write(item + '\n')
        print(f"列表已保存到文件: {file_path}")
    except Exception as e:
        print(f"保存文件 {file_path} 时出错: {str(e)}")


def interactive_mode():
    """交互式模式"""
    print("=== Qwen3.0 文件名关键词处理工具 ===")
    print("此工具将根据A列表的关键词，在B列表的文件名中进行模糊匹配")
    print("匹配到的关键词将被删除，保留剩余的文件名部分")
    print()
    
    # 获取关键词列表（A列表）
    print("1. 输入关键词列表（A列表）")
    print("   a. 从文件读取")
    print("   b. 手动输入")
    
    choice_a = input("请选择关键词输入方式 (a/b): ").strip().lower()
    
    keywords = []
    if choice_a == 'a':
        keywords_file = input("请输入关键词文件路径: ").strip()
        keywords = load_list_from_file(keywords_file)
        if not keywords:
            print("关键词文件为空或读取失败")
            return
    elif choice_a == 'b':
        print("请输入关键词（每行一个，输入空行结束）:")
        while True:
            keyword = input().strip()
            if not keyword:
                break
            keywords.append(keyword)
    else:
        print("无效选择")
        return
    
    if not keywords:
        print("没有关键词需要处理")
        return
    
    print(f"已加载 {len(keywords)} 个关键词: {', '.join(keywords[:5])}{'...' if len(keywords) > 5 else ''}")
    
    # 获取文件名列表（B列表）
    print("\n2. 输入文件名列表（B列表）")
    print("   a. 从文件读取")
    print("   b. 手动输入")
    
    choice_b = input("请选择文件名输入方式 (a/b): ").strip().lower()
    
    filenames = []
    if choice_b == 'a':
        filenames_file = input("请输入文件名文件路径: ").strip()
        filenames = load_list_from_file(filenames_file)
        if not filenames:
            print("文件名文件为空或读取失败")
            return
    elif choice_b == 'b':
        print("请输入文件名（每行一个，输入空行结束）:")
        while True:
            filename = input().strip()
            if not filename:
                break
            filenames.append(filename)
    else:
        print("无效选择")
        return
    
    if not filenames:
        print("没有文件名需要处理")
        return
    
    print(f"已加载 {len(filenames)} 个文件名")
    
    # 选择输出文件名
    output_file = input("\n请输入输出文件名 (默认: filename_results.json): ").strip()
    if not output_file:
        output_file = "filename_results.json"
    
    # 是否保存处理后的文件名列表
    save_remaining = input("是否保存处理后的文件名列表到单独文件? (y/n, 默认: y): ").strip().lower()
    if save_remaining != 'n':
        remaining_file = input("请输入处理后文件名的保存路径 (默认: remaining_filenames.txt): ").strip()
        if not remaining_file:
            remaining_file = "remaining_filenames.txt"
    else:
        remaining_file = None
    
    # 开始处理
    try:
        processor = QwenFileProcessor()
        print(f"\n开始处理 {len(filenames)} 个文件名，使用 {len(keywords)} 个关键词...")
        
        results = processor.process_large_filename_batch(keywords, filenames)
        
        # 保存结果
        processor.save_results(results, output_file)
        
        # 保存处理后的文件名列表
        if remaining_file:
            remaining_filenames = processor.extract_remaining_filenames(results)
            save_list_to_file(remaining_filenames, remaining_file)
        
        # 显示统计信息
        stats = processor.get_processing_stats(results)
        print("\n=== 处理统计 ===")
        print(f"总文件数: {stats['total_files']}")
        print(f"成功处理: {stats['successful']}")
        print(f"处理失败: {stats['failed']}")
        print(f"成功率: {stats['success_rate']:.2%}")
        print(f"匹配文件数: {stats['matched_files']}")
        print(f"匹配率: {stats['match_rate']:.2%}")
        print(f"总token数: {stats['total_tokens']}")
        
        # 显示一些示例结果
        print("\n=== 处理示例 ===")
        for i, result in enumerate(results[:5]):
            if result['status'] == 'success':
                print(f"{i+1}. 原文件名: {result['original_filename']}")
                print(f"   匹配关键词: {', '.join(result['matched_keywords']) if result['matched_keywords'] else '无'}")
                print(f"   处理后: {result['remaining_filename']}")
                print()
        
        if len(results) > 5:
            print(f"... 还有 {len(results) - 5} 个结果，详见输出文件")
        
    except Exception as e:
        print(f"处理过程中出错: {str(e)}")


def batch_mode(keywords_file: str, filenames_file: str, output_file: str, remaining_file: str = None):
    """
    批处理模式
    
    Args:
        keywords_file: 关键词文件路径
        filenames_file: 文件名文件路径
        output_file: 输出文件路径
        remaining_file: 处理后文件名保存路径
    """
    try:
        # 加载关键词
        keywords = load_list_from_file(keywords_file)
        if not keywords:
            print("关键词文件为空或读取失败")
            return
        
        # 加载文件名
        filenames = load_list_from_file(filenames_file)
        if not filenames:
            print("文件名文件为空或读取失败")
            return
        
        # 初始化处理器
        processor = QwenFileProcessor()
        print(f"开始处理 {len(filenames)} 个文件名，使用 {len(keywords)} 个关键词...")
        
        # 批量处理
        results = processor.process_large_filename_batch(keywords, filenames)
        
        # 保存结果
        processor.save_results(results, output_file)
        
        # 保存处理后的文件名列表
        if remaining_file:
            remaining_filenames = processor.extract_remaining_filenames(results)
            save_list_to_file(remaining_filenames, remaining_file)
        
        # 显示统计信息
        stats = processor.get_processing_stats(results)
        print(f"处理完成！成功率: {stats['success_rate']:.2%}，匹配率: {stats['match_rate']:.2%}")
        
    except Exception as e:
        print(f"批处理过程中出错: {str(e)}")


def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 命令行模式
        if len(sys.argv) < 4:
            print("用法: python filename_processor.py <keywords_file> <filenames_file> <output_file> [remaining_file]")
            print("示例: python filename_processor.py keywords.txt filenames.txt results.json remaining.txt")
            return
        
        keywords_file = sys.argv[1]
        filenames_file = sys.argv[2]
        output_file = sys.argv[3]
        remaining_file = sys.argv[4] if len(sys.argv) > 4 else None
        
        batch_mode(keywords_file, filenames_file, output_file, remaining_file)
    else:
        # 交互式模式
        interactive_mode()


if __name__ == '__main__':
    main()
