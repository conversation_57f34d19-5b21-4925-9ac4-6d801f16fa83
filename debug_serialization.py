from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable
import json


pp1='/Users/<USER>/Downloads/统计表(1).xlsx'
dd1=pd.read_excel(pp1,sheet_name='2023年B类新建',skiprows=[0,2,3])
dd1项目名称 = dd1[['项目名称', '项目编码']].dropna().values.tolist()
ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]

data = [i for i in ll1项目名称]

details = {
        row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
        for row in data
    }

def convert_to_simple_tree_data(data, details):
    """转换为简单的树形数据"""
    tree_data = []

    for project in data:
        # 父节点 - 项目
        tree_data.append({
            "id": f"project_{len(tree_data)}",
            "项目名称": f"📁 {project}",
            "标段名称": "",
            "项目总投资": "",
            "type": "project",
            "level": 0,
            "expanded": True
        })

        # 子节点 - 标段
        if project in details:
            for detail in details[project]["rows"]:
                tree_data.append({
                    "id": f"detail_{len(tree_data)}",
                    "项目名称": f"　　📄 {detail['name']}",
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "type": "detail",
                    "level": 1,
                    "expanded": False
                })

    return tree_data

def test_serialization():
    """测试数据序列化"""
    print("=== 测试数据序列化 ===")
    
    # 生成测试数据
    tree_data = convert_to_simple_tree_data(data[:2], details)  # 只测试前2个项目
    
    print(f"生成了 {len(tree_data)} 条数据")
    
    # 测试每条数据的序列化
    for i, item in enumerate(tree_data):
        try:
            json.dumps(item)
            print(f"✅ 数据 {i+1} 可以序列化")
        except Exception as e:
            print(f"❌ 数据 {i+1} 序列化失败: {e}")
            print(f"   问题数据: {item}")
    
    # 测试整体数据序列化
    try:
        json.dumps(tree_data)
        print("✅ 整体数据可以序列化")
    except Exception as e:
        print(f"❌ 整体数据序列化失败: {e}")
    
    return tree_data

def test_aggrid_config():
    """测试AgGrid配置序列化"""
    print("\n=== 测试AgGrid配置序列化 ===")
    
    # 测试有问题的配置
    problematic_config = {
        "columnDefs": [
            {
                "headerName": "项目名称",
                "field": "项目名称",
                "width": 400,
                "cellStyle": lambda params: {  # 这里是问题所在！
                    "fontWeight": "bold" if params['data'].get('level') == 0 else "normal",
                    "paddingLeft": f"{params['data'].get('level', 0) * 20 + 10}px"
                }
            }
        ]
    }
    
    try:
        json.dumps(problematic_config)
        print("✅ 有问题的配置可以序列化")
    except Exception as e:
        print(f"❌ 有问题的配置序列化失败: {e}")
    
    # 测试修复后的配置
    fixed_config = {
        "columnDefs": [
            {
                "headerName": "项目名称",
                "field": "项目名称",
                "width": 400
                # 移除了cellStyle lambda函数
            },
            {
                "headerName": "标段名称",
                "field": "标段名称",
                "width": 200
            },
            {
                "headerName": "项目总投资",
                "field": "项目总投资",
                "width": 200
            }
        ],
        "defaultColDef": {
            "resizable": True,
            "sortable": True,
            "filter": True
        },
        "rowHeight": 40,
        "suppressRowHoverHighlight": False,
        "rowSelection": "single"
    }
    
    try:
        json.dumps(fixed_config)
        print("✅ 修复后的配置可以序列化")
    except Exception as e:
        print(f"❌ 修复后的配置序列化失败: {e}")

@ui.page("/")
def home():
    """完全修复的主页面"""
    
    ui.label("完全修复版本").classes("text-xl font-bold mb-4")
    
    # 生成tree_data
    tree_data = convert_to_simple_tree_data(data, details)
    
    ui.label(f"数据量: {len(tree_data)} 条").classes("text-green-600 mb-4")
    
    # 转换数据
    summary_data = rxui.use_pagination(tree_data, page_size=100)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=10")

        # 完全安全的AgGrid配置 - 不包含任何函数
        grid_config = {
            "columnDefs": [
                {
                    "headerName": "项目名称",
                    "field": "项目名称",
                    "width": 400
                },
                {
                    "headerName": "标段名称",
                    "field": "标段名称",
                    "width": 200
                },
                {
                    "headerName": "项目总投资",
                    "field": "项目总投资",
                    "width": 200
                }
            ],
            "rowData": summary_data.current_source.value,
            "defaultColDef": {
                "resizable": True,
                "sortable": True,
                "filter": True
            },
            "rowHeight": 40,
            "suppressRowHoverHighlight": False,
            "rowSelection": "single"
        }
        
        ui.aggrid(grid_config).classes("w-full h-[500px] border-2 border-gray-300")

@ui.page("/test")
def test_page():
    """测试页面"""
    ui.label("序列化测试页面").classes("text-xl font-bold mb-4")
    
    with ui.column().classes("gap-4"):
        ui.button("测试数据序列化", on_click=lambda: test_serialization())
        ui.button("测试配置序列化", on_click=lambda: test_aggrid_config())
        
        result_area = ui.textarea("测试结果").classes("w-full h-64")
        
        def run_all_tests():
            import io
            import sys
            
            # 捕获print输出
            old_stdout = sys.stdout
            sys.stdout = captured_output = io.StringIO()
            
            try:
                test_serialization()
                test_aggrid_config()
            finally:
                sys.stdout = old_stdout
            
            result_area.value = captured_output.getvalue()
        
        ui.button("运行所有测试", on_click=run_all_tests).classes("bg-blue-500 text-white")

if __name__ == "__main__":
    # 运行测试
    print("启动前测试...")
    test_serialization()
    test_aggrid_config()
    
    # 添加导航
    with ui.header():
        ui.label("调试版本").classes("text-white text-lg font-bold")
        with ui.row():
            ui.link("主页", "/").classes("text-white")
            ui.link("测试", "/test").classes("text-white ml-4")
    
    ui.run()
