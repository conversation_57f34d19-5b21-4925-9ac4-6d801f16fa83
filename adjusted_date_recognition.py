import os
from openai import OpenAI
import pandas as pd
import time
from tqdm import tqdm

# 读取数据
pp1 = '/Users/<USER>/Desktop/2025年项目资料/文件日期/合并后的文件.xlsx'
data1 = pd.read_excel(pp1, sheet_name='Sheet1', engine='calamine')
data11 = data1.dropna(how='all', subset=data1.columns[2:]).fillna(0)
ld11 = data11.values.tolist()

print(f"加载了 {len(ld11)} 行数据")
print(f"每行有 {len(ld11[0]) if ld11 else 0} 列")

# 初始化OpenAI客户端
client = OpenAI(
    api_key="sk-e1bfa2386d234bdf9498f84804075cce",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)


def is_date_and_convert(text):
    """判断文本是否为日期并转换格式，确保返回最简洁的结果"""
    try:
        completion = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {
                    'role': 'system',
                    'content': '你是日期识别专家。严格按要求返回，不要任何解释或多余文字。'
                },
                {
                    'role': 'user',
                    'content': f'判断文本"{text}"是否为日期。\n\n规则：\n- 如果是日期：返回"YYYY年MM月DD日"格式\n- 如果不是日期：只返回"否"\n\n不要返回其他任何内容。'
                }
            ],
            temperature=0,  # 设为0获得最确定的结果
            max_tokens=20,  # 严格限制输出长度
            top_p=0.1  # 进一步限制输出的随机性
        )

        result = completion.choices[0].message.content.strip()

        # 后处理：确保结果简洁
        result = result.replace('。', '').replace('，', '').replace(',', '').replace('.', '')

        # 如果包含否定词汇，统一返回"否"
        negative_words = ['否', '不是', '非', '无法', '不能', '错误', '无效']
        if any(word in result for word in negative_words):
            return "否"

        # 如果包含年月日，提取并格式化
        if '年' in result and ('月' in result or '日' in result):
            return result

        # 其他情况返回原结果或"否"
        return result if len(result) <= 15 else "否"

    except Exception as e:
        return "处理失败"


# 处理二维列表数据
results = []
total_processed = 0
total_skipped = 0

print("开始处理二维列表...")

for row_idx, row in enumerate(tqdm(ld11, desc="处理行数据")):
    # 获取前两列作为标识信息
    if len(row) >= 2:
        identifier1 = row[0] if row[0] != 0 else f"行{row_idx+1}"
        identifier2 = row[1] if row[1] != 0 else ""
    else:
        identifier1 = f"行{row_idx+1}"
        identifier2 = ""
    
    # 从第三个元素开始处理（索引2开始）
    for col_idx in range(2, len(row)):
        value = row[col_idx]
        
        # 跳过值为0的元素
        if value == 0 or value == '0':
            total_skipped += 1
            continue
        
        # 跳过空值
        if pd.isna(value) or value == '':
            total_skipped += 1
            continue
        
        # 处理非0值
        try:
            converted_result = is_date_and_convert(str(value))
            results.append([
                identifier1,           # 第一列标识
                identifier2,           # 第二列标识  
                f"列{col_idx+1}",      # 列位置
                str(value),            # 原始值
                converted_result       # 转换结果
            ])
            
            total_processed += 1
            
            # 显示处理进度（每50个显示一次）
            if total_processed % 50 == 0:
                print(f"已处理 {total_processed} 个值...")
            
            # 添加延迟避免API限制
            time.sleep(0.05)
            
        except Exception as e:
            print(f"处理第{row_idx+1}行第{col_idx+1}列时出错: {e}")
            results.append([
                identifier1,
                identifier2,
                f"列{col_idx+1}",
                str(value),
                "处理失败"
            ])
            total_processed += 1

print(f"\n处理统计:")
print(f"总共处理: {total_processed} 个值")
print(f"跳过的0值: {total_skipped} 个")

# 保存结果
if results:
    result_df = pd.DataFrame(results, columns=["标识1", "标识2", "列位置", "原始值", "转换结果"])
    
    # 保存详细结果
    result_df.to_excel("二维列表日期识别结果.xlsx", index=False, engine='openpyxl')
    
    # 创建汇总结果 - 只保留识别为日期的记录
    date_results = result_df[
        (result_df['转换结果'] != '否') & 
        (result_df['转换结果'] != '处理失败')
    ].copy()
    
    if not date_results.empty:
        date_results.to_excel("识别到的日期汇总.xlsx", index=False, engine='openpyxl')
        print(f"日期汇总已保存到: 识别到的日期汇总.xlsx")
    
    # 输出统计
    total = len(results)
    dates = len(date_results)
    failed = len([r for r in results if r[4] == "处理失败"])
    
    print(f"\n识别结果统计:")
    print(f"总处理数据: {total} 条")
    print(f"识别为日期: {dates} 条")
    print(f"非日期数据: {total - dates - failed} 条")
    print(f"处理失败: {failed} 条")
    print(f"日期识别率: {dates/total*100:.1f}%")
    
    # 显示结果示例
    print(f"\n处理结果示例（前10条）:")
    for i, result in enumerate(results[:10]):
        print(f"{i+1}. {result[0]} | {result[1]} | {result[2]}: {result[3]} -> {result[4]}")
    
    # 显示识别到的日期示例
    if not date_results.empty:
        print(f"\n识别到的日期示例（前10个）:")
        for i, (_, result) in enumerate(date_results.head(10).iterrows()):
            print(f"{i+1}. {result['标识1']} | {result['标识2']} | {result['列位置']}: {result['原始值']} -> {result['转换结果']}")
    
    print(f"\n详细结果已保存到: 二维列表日期识别结果.xlsx")
    
    # 按行统计日期数量
    if not date_results.empty:
        row_stats = date_results.groupby('标识1').size().reset_index(columns=['日期数量'])
        print(f"\n各行日期统计（前10行）:")
        for i, (idx, row) in enumerate(row_stats.head(10).iterrows()):
            print(f"{i+1}. {row['标识1']}: {row['日期数量']} 个日期")
    
else:
    print("没有找到需要处理的数据")

print("\n处理完成！")
