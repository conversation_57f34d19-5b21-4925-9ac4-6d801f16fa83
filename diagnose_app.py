#!/usr/bin/env python3
"""
NiceGUI应用诊断脚本
"""

import sys
import traceback

def check_imports():
    """检查依赖包导入"""
    print("=== 检查依赖包 ===")
    
    packages = [
        ('pandas', 'pd'),
        ('nicegui', 'ui'),
        ('ex4nicegui', 'rxui'),
        ('pathlib', 'Path')
    ]
    
    for package, alias in packages:
        try:
            if alias:
                exec(f"import {package} as {alias}")
            else:
                exec(f"import {package}")
            print(f"✅ {package}: 正常")
        except ImportError as e:
            print(f"❌ {package}: 导入失败 - {e}")
        except Exception as e:
            print(f"⚠️ {package}: 其他错误 - {e}")

def check_file():
    """检查文件是否存在"""
    print("\n=== 检查文件 ===")
    
    file_path = '/Users/<USER>/Downloads/统计表(1).xlsx'
    
    try:
        from pathlib import Path
        p = Path(file_path)
        
        if p.exists():
            print(f"✅ 文件存在: {file_path}")
            print(f"   文件大小: {p.stat().st_size} bytes")
        else:
            print(f"❌ 文件不存在: {file_path}")
            
            # 检查目录
            parent_dir = p.parent
            if parent_dir.exists():
                print(f"📁 目录存在，文件列表:")
                for f in parent_dir.glob("*.xlsx"):
                    print(f"   - {f.name}")
            else:
                print(f"❌ 目录也不存在: {parent_dir}")
                
    except Exception as e:
        print(f"❌ 文件检查失败: {e}")

def test_data_loading():
    """测试数据加载"""
    print("\n=== 测试数据加载 ===")
    
    try:
        import pandas as pd
        
        file_path = '/Users/<USER>/Downloads/统计表(1).xlsx'
        sheet_name = '2023年B类新建'
        
        print(f"读取文件: {file_path}")
        print(f"工作表: {sheet_name}")
        
        # 先读取所有工作表名称
        try:
            xl_file = pd.ExcelFile(file_path)
            print(f"可用工作表: {xl_file.sheet_names}")
        except Exception as e:
            print(f"⚠️ 无法读取工作表列表: {e}")
        
        # 读取数据
        dd1 = pd.read_excel(file_path, sheet_name=sheet_name, skiprows=[0, 2, 3])
        print(f"✅ 数据读取成功")
        print(f"   形状: {dd1.shape}")
        print(f"   列名: {dd1.columns.tolist()}")
        
        # 检查必要的列
        required_cols = ['项目名称', '项目编码']
        for col in required_cols:
            if col in dd1.columns:
                print(f"   ✅ 找到列: {col}")
            else:
                print(f"   ❌ 缺少列: {col}")
        
        # 处理数据
        if all(col in dd1.columns for col in required_cols):
            dd1项目名称 = dd1[required_cols].dropna().values.tolist()
            print(f"   有效数据: {len(dd1项目名称)} 条")
            
            if dd1项目名称:
                print("   前3条数据:")
                for i, row in enumerate(dd1项目名称[:3]):
                    print(f"     {i+1}. {row}")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        traceback.print_exc()

def test_basic_nicegui():
    """测试基础NiceGUI功能"""
    print("\n=== 测试基础NiceGUI ===")
    
    try:
        from nicegui import ui
        
        @ui.page("/test")
        def test_page():
            ui.label("测试页面")
            ui.button("测试按钮")
        
        print("✅ NiceGUI基础功能正常")
        
        # 测试ex4nicegui
        try:
            from ex4nicegui import rxui, effect_refreshable
            print("✅ ex4nicegui导入正常")
        except ImportError as e:
            print(f"❌ ex4nicegui导入失败: {e}")
            print("   请安装: pip install ex4nicegui")
        
    except Exception as e:
        print(f"❌ NiceGUI测试失败: {e}")

def main():
    """主函数"""
    print("🔍 NiceGUI应用诊断开始...\n")
    
    check_imports()
    check_file()
    test_data_loading()
    test_basic_nicegui()
    
    print("\n=== 诊断完成 ===")
    print("如果所有检查都通过，请尝试运行 minimal_nicegui_app.py")
    print("如果仍有问题，请检查以下内容:")
    print("1. 确保文件路径正确")
    print("2. 确保工作表名称正确")
    print("3. 确保所有依赖包已安装")
    print("4. 尝试重启Python环境")

if __name__ == "__main__":
    main()
