from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable
from typing import List, Dict, Tuple, Optional


class ProjectDataManager:
    """项目数据管理器"""
    
    def __init__(self, file_path: str = '/Users/<USER>/Downloads/统计表(1).xlsx'):
        self.file_path = file_path
        
    def load_project_data(self, sheet_name: str = '2023年B类新建', 
                         columns: List[str] = None) -> List[str]:
        """
        加载项目数据
        
        Args:
            sheet_name: 工作表名称
            columns: 要读取的列名列表，默认为['项目名称', '项目编码']
        
        Returns:
            项目数据列表
        """
        if columns is None:
            columns = ['项目名称', '项目编码']
            
        try:
            print(f"正在读取文件: {self.file_path}")
            print(f"工作表: {sheet_name}")
            
            dd1 = pd.read_excel(self.file_path, sheet_name=sheet_name, skiprows=[0, 2, 3])
            print(f"原始数据形状: {dd1.shape}")
            print(f"可用列: {dd1.columns.tolist()}")
            
            # 检查列是否存在
            missing_cols = [col for col in columns if col not in dd1.columns]
            if missing_cols:
                print(f"警告: 缺少列 {missing_cols}")
                available_cols = [col for col in columns if col in dd1.columns]
                if not available_cols:
                    print("错误: 没有可用的列")
                    return []
                columns = available_cols
            
            dd1项目名称 = dd1[columns].dropna().values.tolist()
            print(f"有效项目数据: {len(dd1项目名称)} 条")
            
            # 连接多列数据
            ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]
            data = [i for i in ll1项目名称]
            
            return data
            
        except FileNotFoundError:
            print(f"错误: 文件不存在 {self.file_path}")
            return []
        except Exception as e:
            print(f"加载数据失败: {e}")
            return []

    def create_project_details(self, data: List[str], 
                             detail_count: int = 3,
                             detail_prefix: str = "标段名称") -> Dict:
        """
        创建项目详情数据
        
        Args:
            data: 项目数据列表
            detail_count: 每个项目的详情数量
            detail_prefix: 详情名称前缀
        
        Returns:
            项目详情字典
        """
        details = {
            row: {
                "rows": [
                    {
                        "name": f"{detail_prefix}{j}", 
                        "value": f"{j}"
                    } for j in range(detail_count)
                ]
            }
            for row in data
        }
        return details

    def get_all_data(self, sheet_name: str = '2023年B类新建',
                    columns: List[str] = None,
                    detail_count: int = 3,
                    detail_prefix: str = "标段名称") -> Tuple[List[str], Dict]:
        """
        一次性获取所有数据
        
        Args:
            sheet_name: 工作表名称
            columns: 要读取的列名列表
            detail_count: 每个项目的详情数量
            detail_prefix: 详情名称前缀
        
        Returns:
            (data, details) 元组
        """
        data = self.load_project_data(sheet_name, columns)
        details = self.create_project_details(data, detail_count, detail_prefix)
        return data, details


# 全局数据管理器实例
data_manager = ProjectDataManager()


def create_project_table_columns(name_label: str = "标段名称", 
                                value_label: str = "项目总投资") -> List[Dict]:
    """
    创建项目表格列配置
    
    Args:
        name_label: 名称列标签
        value_label: 值列标签
    
    Returns:
        列配置列表
    """
    return [
        {"name": "name", "field": "name", "label": name_label},
        {"name": "value", "field": "value", "label": value_label},
    ]


@ui.page("/")
def home():
    """主页面"""
    
    # 页面配置
    PAGE_SIZE = 100
    MAX_PAGES = 10
    
    # 获取数据
    try:
        data, details = data_manager.get_all_data(
            sheet_name='2023年B类新建',
            columns=['项目名称', '项目编码'],
            detail_count=3,
            detail_prefix="标段名称"
        )
        
        print(f"✅ 成功加载 {len(data)} 个项目")
        
        if details:
            print("📋 详情数据示例:")
            for i, key in enumerate(list(details.keys())[:3]):
                print(f"  {i+1}. {key}")
        
    except Exception as e:
        print(f"❌ 数据加载失败: {e}")
        data, details = [], {}
    
    # 检查数据
    if not data:
        with ui.column().classes("p-4"):
            ui.label("⚠️ 没有加载到数据").classes("text-red-500 text-lg")
            ui.label("请检查以下项目:").classes("text-gray-600")
            ui.label("• 文件路径是否正确").classes("text-gray-500")
            ui.label("• 工作表名称是否存在").classes("text-gray-500")
            ui.label("• 文件格式是否正确").classes("text-gray-500")
        return
    
    # 创建分页数据
    summary_data = rxui.use_pagination(data, page_size=PAGE_SIZE)
    
    # 创建表格列配置
    table_columns = create_project_table_columns("标段名称", "项目总投资")

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        # 分页控件
        summary_data.create_q_pagination().props(f"max-pages={MAX_PAGES}")

        # 项目列表
        with ui.column().classes("gap-0 w-full items-stretch"):
            for row in summary_data.current_source.value:
                with ui.expansion(text=row).classes(
                    "border-b-2 border-gray-300"
                ).props("dense"):
                    if row in details:
                        ui.table(
                            rows=details[row]["rows"],
                            columns=table_columns,
                        )
                    else:
                        ui.label("暂无详情数据").classes("text-gray-500 p-2")


@ui.page("/config")
def config_page():
    """配置页面"""
    ui.label("配置页面").classes("text-2xl font-bold mb-4")
    
    with ui.column().classes("gap-4 max-w-md"):
        ui.input("文件路径", value=data_manager.file_path).classes("w-full")
        ui.input("工作表名称", value="2023年B类新建").classes("w-full")
        ui.number("详情数量", value=3, min=1, max=10).classes("w-full")
        ui.input("详情前缀", value="标段名称").classes("w-full")
        
        ui.button("重新加载数据", on_click=lambda: ui.navigate.to("/")).classes("mt-4")


if __name__ == "__main__":
    # 添加导航
    with ui.header():
        ui.label("项目管理系统").classes("text-xl font-bold")
        with ui.row():
            ui.link("首页", "/").classes("text-white")
            ui.link("配置", "/config").classes("text-white ml-4")
    
    ui.run()
