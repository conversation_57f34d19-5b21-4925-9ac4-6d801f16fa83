import pandas as pd

# 您的原始代码
dd1 = pd.read_excel(pp1, sheet_name='2025年B类新建', skiprows=[0,1,2])
print(dd1.head(5))

dd1项目名称 = dd1[['项目名称','项目编码']].dropna().values.tolist()
print(dd1项目名称)

ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]
data = [i for i in ll1项目名称]

# 修改后的details - 用dd1的实际数据替换range(3)
details = {}

for i, row_key in enumerate(data):
    # 获取对应的dd1原始数据
    original_data = dd1项目名称[i]  # [项目名称, 项目编码]
    
    # 方案1: 使用项目名称和编码
    details[row_key] = {
        "rows": [
            {"name": "项目名称", "value": str(original_data[0])},
            {"name": "项目编码", "value": str(original_data[1])},
            {"name": "序号", "value": str(i+1)}  # 添加序号作为第三个值
        ]
    }

print("方案1结果:")
for key, value in list(details.items())[:3]:
    print(f"{key}: {value}")

# 方案2: 如果您想要dd1的更多列数据
details_v2 = {}

for i, row_key in enumerate(data):
    # 获取dd1中对应行的完整数据
    full_row = dd1.iloc[i]
    
    # 提取前3个非空值
    non_null_values = [val for val in full_row if pd.notna(val)][:3]
    
    details_v2[row_key] = {
        "rows": [
            {"name": f"字段{j+1}", "value": str(val)}
            for j, val in enumerate(non_null_values)
        ]
    }

print("\n方案2结果:")
for key, value in list(details_v2.items())[:3]:
    print(f"{key}: {value}")

# 方案3: 使用dd1的列名作为name
details_v3 = {}

for i, row_key in enumerate(data):
    full_row = dd1.iloc[i]
    
    # 获取前3个非空的列名和值
    rows_data = []
    count = 0
    for col_name, val in full_row.items():
        if pd.notna(val) and count < 3:
            rows_data.append({
                "name": str(col_name),
                "value": str(val)
            })
            count += 1
    
    details_v3[row_key] = {"rows": rows_data}

print("\n方案3结果:")
for key, value in list(details_v3.items())[:3]:
    print(f"{key}: {value}")

# 最简洁的替换版本（推荐）
details = {
    row: {
        "rows": [
            {"name": "项目名称", "value": str(dd1项目名称[i][0])},
            {"name": "项目编码", "value": str(dd1项目名称[i][1])},
            {"name": "行号", "value": str(i+1)}
        ]
    }
    for i, row in enumerate(data)
}

print("\n最终推荐版本:")
for key, value in list(details.items())[:3]:
    print(f"{key}: {value}")
