import pandas as pd

# 读取数据
dd1 = pd.read_excel(pp1, sheet_name='2025年B类新建', skiprows=[0,1,2])
print(dd1.head(5))

# 获取项目名称和编码
dd1项目名称 = dd1[['项目名称','项目编码']].dropna().values.tolist()
print(dd1项目名称)

# 创建项目名称列表
ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]
data = [i for i in ll1项目名称]

# 直接替换 - 使用dd1的实际数据替换range(3)
details = {
    row: {
        "rows": [
            {"name": dd1.columns[0], "value": str(dd1.iloc[i][dd1.columns[0]])},
            {"name": dd1.columns[1], "value": str(dd1.iloc[i][dd1.columns[1]])},
            {"name": dd1.columns[2] if len(dd1.columns) > 2 else "序号", 
             "value": str(dd1.iloc[i][dd1.columns[2]]) if len(dd1.columns) > 2 else str(i+1)}
        ]
    }
    for i, row in enumerate(data)
}

# 显示结果
print("\n替换后的details:")
for key, value in list(details.items())[:3]:  # 显示前3个
    print(f"{key}: {value}")
