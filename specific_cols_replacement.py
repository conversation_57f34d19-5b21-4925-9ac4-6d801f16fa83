import pandas as pd

# 读取数据
dd1 = pd.read_excel(pp1, sheet_name='2025年B类新建', skiprows=[0,1,2])
print(dd1.head(5))

# 指定需要的列
desired_cols = ["标段名称", "标段编号", "合同名称","合同编号","合同金额","中标厂家",'是否跨项目签合同','本标段对应合同金额']

# 获取项目名称和编码（保持原有逻辑）
dd1项目名称 = dd1[['项目名称','项目编码']].dropna().values.tolist()
print(dd1项目名称)

ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]
data = [i for i in ll1项目名称]

# 方案1: 使用指定列的前3个字段
details_v1 = {}
available_cols = [col for col in desired_cols if col in dd1.columns][:3]  # 只取前3个存在的列

for i, row_key in enumerate(data):
    rows_data = []
    for col in available_cols:
        value = dd1.iloc[i][col] if col in dd1.columns else ""
        rows_data.append({
            "name": col,
            "value": str(value) if pd.notna(value) else ""
        })
    
    details_v1[row_key] = {"rows": rows_data}

print("方案1 - 使用前3个可用列:")
print(f"使用的列: {available_cols}")
for key, value in list(details_v1.items())[:2]:
    print(f"{key}: {value}")

# 方案2: 循环使用所有指定列，每次取3个
details_v2 = {}
available_cols = [col for col in desired_cols if col in dd1.columns]

for i, row_key in enumerate(data):
    # 为每行循环选择3个列（可以根据行号轮换）
    start_idx = (i * 3) % len(available_cols) if available_cols else 0
    selected_cols = []
    
    for j in range(3):
        if available_cols:
            col_idx = (start_idx + j) % len(available_cols)
            selected_cols.append(available_cols[col_idx])
    
    rows_data = []
    for col in selected_cols:
        value = dd1.iloc[i][col] if col in dd1.columns else ""
        rows_data.append({
            "name": col,
            "value": str(value) if pd.notna(value) else ""
        })
    
    details_v2[row_key] = {"rows": rows_data}

print("\n方案2 - 循环使用所有列:")
for key, value in list(details_v2.items())[:2]:
    print(f"{key}: {value}")

# 方案3: 使用所有指定列中的非空值，限制为3个
details_v3 = {}
available_cols = [col for col in desired_cols if col in dd1.columns]

for i, row_key in enumerate(data):
    rows_data = []
    count = 0
    
    for col in available_cols:
        if count >= 3:
            break
        value = dd1.iloc[i][col] if col in dd1.columns else ""
        if pd.notna(value) and str(value).strip() != "":  # 只添加非空值
            rows_data.append({
                "name": col,
                "value": str(value)
            })
            count += 1
    
    # 如果非空值不足3个，用空值补齐
    while len(rows_data) < 3 and len(rows_data) < len(available_cols):
        remaining_cols = [col for col in available_cols if col not in [r["name"] for r in rows_data]]
        if remaining_cols:
            col = remaining_cols[0]
            value = dd1.iloc[i][col] if col in dd1.columns else ""
            rows_data.append({
                "name": col,
                "value": str(value) if pd.notna(value) else ""
            })
    
    details_v3[row_key] = {"rows": rows_data}

print("\n方案3 - 优先使用非空值:")
for key, value in list(details_v3.items())[:2]:
    print(f"{key}: {value}")

# 推荐方案: 固定使用前3个重要列
details = {}
# 按重要性排序，选择前3个存在的列
priority_cols = ["标段名称", "合同名称", "合同金额"]  # 可以根据需要调整优先级
available_priority_cols = [col for col in priority_cols if col in dd1.columns]

# 如果优先列不足3个，从剩余列中补充
if len(available_priority_cols) < 3:
    remaining_cols = [col for col in desired_cols if col not in available_priority_cols]
    available_remaining = [col for col in remaining_cols if col in dd1.columns]
    available_priority_cols.extend(available_remaining[:3-len(available_priority_cols)])

final_cols = available_priority_cols[:3]  # 确保只有3个列

for i, row_key in enumerate(data):
    details[row_key] = {
        "rows": [
            {
                "name": col,
                "value": str(dd1.iloc[i][col]) if col in dd1.columns and pd.notna(dd1.iloc[i][col]) else ""
            }
            for col in final_cols
        ]
    }

print(f"\n最终推荐方案 - 使用列: {final_cols}")
for key, value in list(details.items())[:3]:
    print(f"{key}: {value}")

# 显示可用的列信息
print(f"\ndd1中存在的指定列: {[col for col in desired_cols if col in dd1.columns]}")
print(f"dd1的所有列: {dd1.columns.tolist()}")
