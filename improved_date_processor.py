import os
from openai import OpenAI
import pandas as pd
import time
from tqdm import tqdm

# 读取数据
pp1='/Users/<USER>/Desktop/2025年项目资料/文件日期/合并后的文件.xlsx'
data1=pd.read_excel(pp1,sheet_name='Sheet1',engine='calamine')[['文件路径',1]].dropna().iloc[0:50]
print(f"加载了 {len(data1.values)} 条数据")

print("数据预览:")
print(data1.head())

# 初始化OpenAI客户端
client = OpenAI(
    api_key="sk-e1bfa2386d234bdf9498f84804075cce",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)


def is_date_and_convert(text):
    """判断文本是否为日期并转换格式，返回简洁结果"""
    try:
        completion = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {
                    'role': 'system', 
                    'content': '你是日期识别专家。只返回简洁结果，不要解释。'
                },
                {
                    'role': 'user', 
                    'content': f'文本：{text}\n\n如果是日期，转换为"YYYY年MM月DD日"格式；如果不是日期，只返回"否"。'
                }
            ],
            temperature=0.1,  # 降低温度获得更一致的结果
            max_tokens=30     # 限制输出长度
        )
        
        result = completion.choices[0].message.content.strip()
        
        # 进一步清理结果，确保简洁
        if "否" in result or "不是" in result or "非日期" in result:
            return "否"
        elif "年" in result and "月" in result:
            # 提取日期部分
            return result
        else:
            return result
            
    except Exception as e:
        print(f"API调用失败: {e}")
        return "处理失败"


# 准备数据
samples = data1.values.tolist()
print(f"\n开始处理 {len(samples)} 条数据...")
print("前5条样本数据:")
for i, (fname, text) in enumerate(samples[:5], 1):
    print(f"{i}. {os.path.basename(fname)}: {text}")

# 处理数据
results = []
failed_count = 0

for i, (fname, text) in enumerate(tqdm(samples, desc="处理日期识别")):
    try:
        converted_result = is_date_and_convert(text)
        results.append([fname, text, converted_result])
        
        # 每处理10个显示一次进度
        if (i + 1) % 10 == 0:
            current_date_count = len([r for r in results if r[2] != "否" and r[2] != "处理失败"])
            print(f"已处理 {i+1}/{len(samples)} 条，识别到日期 {current_date_count} 个")
        
        # 添加短暂延迟避免API限制
        time.sleep(0.1)
        
    except Exception as e:
        print(f"处理第 {i+1} 条数据时出错: {e}")
        results.append([fname, text, "处理失败"])
        failed_count += 1

# 显示处理结果示例
print(f"\n=== 处理结果示例（前15条） ===")
for i, (fname, original, result) in enumerate(results[:15], 1):
    filename = os.path.basename(fname) if len(fname) > 50 else fname
    status = "✓" if result != "否" and result != "处理失败" else "✗"
    print(f"{i:2d}. {status} {filename}")
    print(f"    原文本: {original}")
    print(f"    结果: {result}")
    print()

# 保存结果到Excel
result_df = pd.DataFrame(results, columns=["文件路径", "原始文本", "转换结果"])
output_file = "处理后的日期结果.xlsx"

try:
    result_df.to_excel(output_file, index=False, engine='openpyxl')
    print(f"结果已保存到: {output_file}")
except Exception as e:
    print(f"保存Excel文件失败: {e}")
    # 尝试保存为CSV
    csv_file = "处理后的日期结果.csv"
    result_df.to_csv(csv_file, index=False, encoding='utf-8-sig')
    print(f"已保存为CSV文件: {csv_file}")

# 统计分析
total_count = len(results)
date_count = len([r for r in results if r[2] != "否" and r[2] != "处理失败"])
non_date_count = len([r for r in results if r[2] == "否"])
error_count = len([r for r in results if r[2] == "处理失败"])

print(f"\n=== 处理统计 ===")
print(f"总数据量: {total_count}")
print(f"识别为日期: {date_count} ({date_count/total_count*100:.1f}%)")
print(f"非日期格式: {non_date_count} ({non_date_count/total_count*100:.1f}%)")
print(f"处理失败: {error_count} ({error_count/total_count*100:.1f}%)")

# 显示识别到的日期示例
date_results = [r for r in results if r[2] != "否" and r[2] != "处理失败"]
if date_results:
    print(f"\n=== 识别到的日期示例（前10个） ===")
    for i, (fname, original, result) in enumerate(date_results[:10], 1):
        filename = os.path.basename(fname)
        print(f"{i}. {filename}")
        print(f"   原文本: {original}")
        print(f"   转换后: {result}")
        print()
else:
    print("\n没有识别到任何日期格式的文本")

# 显示非日期的示例
non_date_results = [r for r in results if r[2] == "否"]
if non_date_results:
    print(f"\n=== 非日期文本示例（前5个） ===")
    for i, (fname, original, result) in enumerate(non_date_results[:5], 1):
        filename = os.path.basename(fname)
        print(f"{i}. {filename}: {original}")

print(f"\n处理完成！详细结果请查看 {output_file}")
