import os
from openai import OpenAI
import pandas as pd
import time
from tqdm import tqdm

# 读取数据
pp1 = '/Users/<USER>/Desktop/2025年项目资料/文件日期/合并后的文件.xlsx'
data1 = pd.read_excel(pp1, sheet_name='Sheet1', engine='calamine')
data11 = data1.dropna(how='all', subset=data1.columns[2:]).fillna(0)

# 直接从data11创建ld22 - 删掉前2个元素
print("从data11直接创建ld22（删掉前2列）...")
ld22_data = data11.iloc[:, 2:]  # 删掉前2列
ld22 = ld22_data.values.tolist()

# 同时保留ld11用于后续整合
ld11 = data11.values.tolist()

print(f"原始data11: {data11.shape[0]} 行 x {data11.shape[1]} 列")
print(f"创建的ld22: {len(ld22)} 行 x {len(ld22[0]) if ld22 and ld22[0] else 0} 列")
print(f"保留的ld11: {len(ld11)} 行 x {len(ld11[0]) if ld11 else 0} 列")

# 初始化OpenAI客户端
client = OpenAI(
    api_key="sk-e1bfa2386d234bdf9498f84804075cce",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)


def is_date_and_convert(text):
    """判断文本是否为日期并转换格式"""
    try:
        completion = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {
                    'role': 'system',
                    'content': '你是日期识别专家。严格按要求返回，不要解释。'
                },
                {
                    'role': 'user',
                    'content': f'判断"{text}"是否为日期。是日期返回"YYYY年MM月DD日"格式，不是返回"否"。'
                }
            ],
            temperature=0,
            max_tokens=20,
            top_p=0.1
        )

        result = completion.choices[0].message.content.strip()
        result = result.replace('。', '').replace('，', '').replace(',', '').replace('.', '')

        # 检查否定词
        if any(word in result for word in ['否', '不是', '非', '无法', '不能']):
            return "否"

        # 检查日期格式
        if '年' in result and ('月' in result or '日' in result):
            return result

        return result if len(result) <= 15 else "否"

    except Exception as e:
        return "处理失败"


# 步骤2: 处理ld22数据
print("步骤2: 处理ld22数据...")
processed_count = 0
skipped_count = 0
ld22_results = []  # 存储每行的识别结果

for row_idx, row in enumerate(tqdm(ld22, desc="处理ld22")):
    row_results = []  # 当前行的结果
    
    for col_idx, value in enumerate(row):
        # 跳过0值和空值
        if value == 0 or value == '0' or pd.isna(value) or value == '':
            row_results.append("跳过")
            skipped_count += 1
            continue
        
        # 处理非0值
        date_result = is_date_and_convert(str(value))
        row_results.append(date_result)
        processed_count += 1
        
        # 每处理50个显示进度
        if processed_count % 50 == 0:
            print(f"已处理 {processed_count} 个值")
        
        # API延迟
        time.sleep(0.05)
    
    ld22_results.append(row_results)

print(f"ld22处理完成: 处理了 {processed_count} 个值，跳过了 {skipped_count} 个值")

# 步骤3: 整合结果回ld11
print("步骤3: 整合结果回ld11...")
ld11_integrated = []

for row_idx in range(len(ld11)):
    # 开始构建新行
    new_row = []
    
    # 1. 保留原始ld11的前2列
    if len(ld11[row_idx]) >= 2:
        new_row.extend(ld11[row_idx][:2])
    elif len(ld11[row_idx]) == 1:
        new_row.extend([ld11[row_idx][0], ""])
    else:
        new_row.extend(["", ""])
    
    # 2. 添加原始数据（第3列开始）
    if len(ld11[row_idx]) > 2:
        new_row.extend(ld11[row_idx][2:])
    
    # 3. 添加识别结果
    if row_idx < len(ld22_results):
        new_row.extend(ld22_results[row_idx])
    
    ld11_integrated.append(new_row)

print(f"整合完成: {len(ld11_integrated)} 行 x {len(ld11_integrated[0]) if ld11_integrated else 0} 列")

# 步骤4: 保存结果
print("步骤4: 保存结果...")

# 创建详细分析结果
detailed_results = []
for row_idx in range(len(ld11)):
    # 获取行标识
    row_id = ld11[row_idx][0] if len(ld11[row_idx]) > 0 and ld11[row_idx][0] != 0 else f"行{row_idx+1}"
    row_desc = ld11[row_idx][1] if len(ld11[row_idx]) > 1 and ld11[row_idx][1] != 0 else ""
    
    # 分析每个处理的值
    if row_idx < len(ld22_results):
        for col_idx, (original_value, result_value) in enumerate(zip(ld22[row_idx], ld22_results[row_idx])):
            if result_value != "跳过":
                detailed_results.append([
                    row_id,
                    row_desc,
                    f"第{col_idx+3}列",  # +3因为去掉了前2列
                    str(original_value),
                    result_value
                ])

# 保存详细结果
if detailed_results:
    detail_df = pd.DataFrame(detailed_results, columns=["行标识", "行描述", "列位置", "原始值", "识别结果"])
    detail_df.to_excel("ld22处理详细结果.xlsx", index=False, engine='openpyxl')

# 保存整合后的完整结果
# 构建列名
max_cols = max(len(row) for row in ld11_integrated) if ld11_integrated else 0
original_data_cols = len(ld11[0]) if ld11 else 0
result_cols = max_cols - original_data_cols

column_names = []
column_names.extend([f"标识列{i+1}" for i in range(2)])  # 前2列
column_names.extend([f"原始数据列{i+3}" for i in range(original_data_cols - 2)])  # 原始数据列
column_names.extend([f"识别结果列{i+1}" for i in range(result_cols)])  # 识别结果列

# 确保列名数量匹配
while len(column_names) < max_cols:
    column_names.append(f"额外列{len(column_names)+1}")

integrated_df = pd.DataFrame(ld11_integrated, columns=column_names[:max_cols])
integrated_df.to_excel("ld11整合后完整结果.xlsx", index=False, engine='openpyxl')

# 统计信息
if detailed_results:
    detail_df = pd.DataFrame(detailed_results, columns=["行标识", "行描述", "列位置", "原始值", "识别结果"])
    total = len(detailed_results)
    dates = len(detail_df[detail_df['识别结果'].str.contains('年', na=False)])
    non_dates = len(detail_df[detail_df['识别结果'] == '否'])
    failed = len(detail_df[detail_df['识别结果'] == '处理失败'])
    
    print(f"\n=== 最终统计 ===")
    print(f"原始ld11: {len(ld11)} 行 x {len(ld11[0]) if ld11 else 0} 列")
    print(f"处理ld22: {len(ld22)} 行 x {len(ld22[0]) if ld22 and ld22[0] else 0} 列")
    print(f"整合结果: {len(ld11_integrated)} 行 x {len(ld11_integrated[0]) if ld11_integrated else 0} 列")
    print(f"处理数据: {processed_count} 个")
    print(f"跳过数据: {skipped_count} 个")
    print(f"识别为日期: {dates} 个")
    print(f"非日期: {non_dates} 个")
    print(f"处理失败: {failed} 个")
    print(f"日期识别率: {dates/total*100:.1f}%")
    
    # 显示示例
    print(f"\n=== 处理示例 ===")
    for i, (_, row) in enumerate(detail_df.head(5).iterrows()):
        print(f"{i+1}. {row['行标识']} | {row['列位置']}: {row['原始值']} -> {row['识别结果']}")
    
    # 显示整合结果示例
    print(f"\n=== 整合结果示例（前3行，前8列） ===")
    for i, row in enumerate(ld11_integrated[:3]):
        display_row = row[:8] if len(row) > 8 else row
        print(f"第{i+1}行: {display_row}")
    
    # 保存仅日期结果
    date_df = detail_df[detail_df['识别结果'].str.contains('年', na=False)]
    if not date_df.empty:
        date_df.to_excel("ld22仅日期结果.xlsx", index=False, engine='openpyxl')
        print(f"\n识别到的日期（前5个）:")
        for i, (_, row) in enumerate(date_df.head(5).iterrows()):
            print(f"{i+1}. {row['行标识']} | {row['列位置']}: {row['原始值']} -> {row['识别结果']}")

print(f"\n=== 保存的文件 ===")
print(f"1. ld22处理详细结果.xlsx - 详细的处理过程")
print(f"2. ld11整合后完整结果.xlsx - 原始数据+识别结果")
print(f"3. ld22仅日期结果.xlsx - 只包含日期的结果")

print(f"\n=== 变量说明 ===")
print(f"ld11: 原始二维列表")
print(f"ld22: 去掉前2列的二维列表")
print(f"ld22_results: ld22的识别结果")
print(f"ld11_integrated: 整合后的完整结果")

print("\n程序执行完毕！")
