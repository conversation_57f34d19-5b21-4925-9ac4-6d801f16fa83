from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable
import warnings
import plotly.graph_objects as go
import plotly.express as px
from collections import Counter


def df_to_custom_dict(df, key_cols, value_cols=None):
    if value_cols is None:
        value_cols = [col for col in df.columns if col not in key_cols]

    result = {}
    for _, row in df.iterrows():
        key = tuple(str(row[col]) for col in key_cols)
        value = {col: row[col] for col in value_cols}
        result.setdefault(key, []).append(value)

    return result


def cal_result(df: pd.DataFrame, ffill_col: list[str]):
    gp_df = df.groupby(
        ffill_col, dropna=False
    )  # 把项目名称+项目编码合并成一个字符串做聚合

    return [
        {"key": tuple(str(k) for k in key), "records": group.to_dict(orient="records")}
        for key, group in gp_df
    ]


warnings.filterwarnings("ignore")
pd.set_option("expand_frame_repr", False)
pd.set_option("display.max_columns", None)
pd.set_option("display.max_rows", None)
pd.set_option("display.precision", 2)
pd.set_option("display.float_format", "{:,.2f}".format)

pp1 = '/Users/<USER>/Downloads/统计表-2023、2024、2025年A、B类.xlsx'
dd1 = (pd.read_excel(pp1, sheet_name="2025年B类新建", skiprows=[0, 1, 2])._append(pd.read_excel(pp1,sheet_name='2025年A类新建',skiprows=[0,1,2])).drop(
    columns="序号"
))
dd1[['开始年','结束年']]=dd1[['开始年','结束年']].astype('str')
dd2 = pd.read_excel(pp1, sheet_name="2024年B类新建", skiprows=[0, 1, 2]).drop(
    columns="序号"
)
dd2[['开始年','结束年']]=dd2[['开始年','结束年']].astype('str')
dd3 = pd.read_excel(pp1, sheet_name="2023年B类新建", skiprows=[0, 1, 2]).drop(
    columns="序号"
)
dd3[['开始年','结束年']]=dd3[['开始年','结束年']].astype('str')
ffill_col = [
    "项目名称",
    "项目编码",
    "建设单位",
    "二级单位",
    "建设性质",
    "管理类别",
    "项目性质",
    "开始年",
    "结束年",
    "项目总投资合计",
    "项目总投资资本性",
    "项目总投资费用性",
    "分部",
    "项目负责人"
]
dd1[ffill_col] = dd1[ffill_col].ffill()
dd2[ffill_col] = dd2[ffill_col].ffill()
dd3[ffill_col] = dd3[ffill_col].ffill()
desired_cols = [
    "标段编号",
    "标段名称",
    "标段金额合计",
    "标段金额资本性",
    "标段金额费用性",
    "合同名称",
    "合同编号",
    "合同金额",
    "中标厂家",
    "是否跨项目签合同",
    "本标段对应合同金额",
    "结算方式",
    "是否暂定价",
    "分部",
    "合同负责人",
    "已支付金额",
    "未支付金额",
    "主标段采购进度",
    "是否主标段",
    "备注",
]

# ---数据处理---
result2025 = cal_result(dd1, ffill_col)
details2025 = df_to_custom_dict(dd1, ffill_col)

result2024 = cal_result(dd2, ffill_col)
details2024 = df_to_custom_dict(dd2, ffill_col)

result2023 = cal_result(dd3, ffill_col)
details2023 = df_to_custom_dict(dd3, ffill_col)

panel_rows_2025 = [row["key"] for row in result2025]
panel_rows_2024 = [row["key"] for row in result2024]
panel_rows_2023 = [row["key"] for row in result2023]

column_frs = "200px 120px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px"

# 全局筛选状态 - 增加投资批次、开始年、结束年筛选
filter_states = {
    2025: {"batch": "全部", "unit": "全部", "nature": "全部","level":"全部","partment":"全部","charge":"全部","investment_min": None, "investment_max": None, "start_year": "全部", "end_year": "全部"},
    2024: {"batch": "全部", "unit": "全部", "nature": "全部","level":"全部","partment":"全部","charge":"全部","investment_min": None, "investment_max": None, "start_year": "全部", "end_year": "全部"},
    2023: {"batch": "全部", "unit": "全部", "nature": "全部","level":"全部","partment":"全部","charge":"全部","investment_min": None, "investment_max": None, "start_year": "全部", "end_year": "全部"}
}


def filter_rows(panel_rows, year):
    """根据筛选条件过滤数据 - 增加投资批次、开始年、结束年筛选"""
    state = filter_states[year]
    filtered = panel_rows

    # 按投资批次筛选（假设在建设性质字段，索引4）
    if state["batch"] != "全部":
        filtered = [row for row in filtered if row[4] == state["batch"]]

    if state["unit"] != "全部":
        filtered = [row for row in filtered if row[2] == state["unit"]]

    if state["nature"] != "全部":
        filtered = [row for row in filtered if row[6] == state["nature"]]

    if state["level"] != "全部":
        filtered = [row for row in filtered if row[5] == state["level"]]

    if state["partment"] != "全部":
        filtered = [row for row in filtered if row[12] == state["partment"]]

    if state["charge"] != "全部":
        filtered = [row for row in filtered if row[13] == state["charge"]]

    # 按开始年筛选（索引7）
    if state["start_year"] != "全部":
        filtered = [row for row in filtered if str(row[7]) == str(state["start_year"])]

    # 按结束年筛选（索引8）
    if state["end_year"] != "全部":
        filtered = [row for row in filtered if str(row[8]) == str(state["end_year"])]

    # 按照项目总投资范围筛选
    if state["investment_min"] is not None or state["investment_max"] is not None:
        def check_investment_range(row):
            investment = safe_float_convert(row[9])  # 项目总投资合计在索引9

            if state["investment_min"] is not None and investment < state["investment_min"]:
                return False
            if state["investment_max"] is not None and investment > state["investment_max"]:
                return False
            return True

        filtered = [row for row in filtered if check_investment_range(row)]

    return filtered


def safe_float_convert(value):
    """安全地将值转换为浮点数"""
    if pd.isna(value) or value == '' or value is None:
        return 0.0
    
    try:
        # 移除逗号和其他非数字字符，保留小数点和负号
        clean_str = str(value).replace(',', '').replace(' ', '')
        if clean_str == '' or clean_str == 'nan':
            return 0.0
        return float(clean_str)
    except (ValueError, TypeError):
        return 0.0


def calculate_statistics(filtered_rows, details, year):
    """计算筛选后数据的统计信息 - 统计所有筛选后的数据，不仅仅是当前分页"""
    if not filtered_rows:
        return {
            "项目数量": 0,
            "总投资合计": 0,
            "总投资资本性": 0,
            "总投资费用性": 0,
            "标段数量": 0,
            "合同总金额": 0,
            "已支付金额": 0,
            "未支付金额": 0
        }

    stats = {
        "项目数量": len(filtered_rows),
        "总投资合计": 0,
        "总投资资本性": 0,
        "总投资费用性": 0,
        "标段数量": 0,
        "合同总金额": 0,
        "已支付金额": 0,
        "未支付金额": 0
    }

    # 用于避免重复统计项目级别数据
    processed_projects = set()

    for row_key in filtered_rows:
        if row_key in details:
            # 项目级别统计（只统计一次，避免重复）
            project_id = f"{row_key[0]}_{row_key[1]}"  # 项目名称+项目编码作为唯一标识
            
            if project_id not in processed_projects:
                processed_projects.add(project_id)
                
                project_investment = safe_float_convert(row_key[9])  # 项目总投资合计
                capital_investment = safe_float_convert(row_key[10])  # 项目总投资资本性
                expense_investment = safe_float_convert(row_key[11])  # 项目总投资费用性

                stats["总投资合计"] += project_investment
                stats["总投资资本性"] += capital_investment
                stats["总投资费用性"] += expense_investment

            # 标段级别统计 - 统计每个标段
            for record in details[row_key]:
                stats["标段数量"] += 1

                # 合同金额统计
                contract_amount = safe_float_convert(record.get("合同金额", 0))
                stats["合同总金额"] += contract_amount

                # 已支付金额统计
                paid_amount = safe_float_convert(record.get("已支付金额", 0))
                stats["已支付金额"] += paid_amount

                # 未支付金额统计
                unpaid_amount = safe_float_convert(record.get("未支付金额", 0))
                stats["未支付金额"] += unpaid_amount

    return stats


def format_currency(amount):
    """格式化金额显示"""
    if amount >= 100000000:  # 亿
        return f"{amount/100000000:.2f}亿"
    elif amount >= 10000:  # 万
        return f"{amount/10000:.2f}万"
    else:
        return f"{amount:.2f}"


def create_visualization_charts(panel_rows, details, year):
    """创建可视化图表"""
    charts = {}

    # 1. 按建设单位统计项目数量
    unit_counts = Counter([row[2] for row in panel_rows])
    charts['unit_chart'] = go.Figure(data=[
        go.Bar(x=list(unit_counts.keys()), y=list(unit_counts.values()))
    ])
    charts['unit_chart'].update_layout(
        title="各建设单位项目数量统计",
        xaxis_title="建设单位",
        yaxis_title="项目数量"
    )

    # 2. 按项目性质统计
    nature_counts = Counter([row[6] for row in panel_rows])
    charts['nature_chart'] = go.Figure(data=[
        go.Pie(labels=list(nature_counts.keys()), values=list(nature_counts.values()))
    ])
    charts['nature_chart'].update_layout(title="项目性质分布")

    # 3. 按投资金额统计
    investments = []
    for row in panel_rows:
        investment = safe_float_convert(row[9])
        if investment > 0:
            investments.append(investment)

    if investments:
        charts['investment_chart'] = go.Figure(data=[
            go.Histogram(x=investments, nbinsx=20)
        ])
        charts['investment_chart'].update_layout(
            title="项目投资金额分布",
            xaxis_title="投资金额(万元)",
            yaxis_title="项目数量"
        )

    # 4. 按年度统计
    start_year_counts = Counter([str(row[7]) for row in panel_rows if row[7] and str(row[7]).strip() != ''])
    end_year_counts = Counter([str(row[8]) for row in panel_rows if row[8] and str(row[8]).strip() != ''])

    years = sorted(set(list(start_year_counts.keys()) + list(end_year_counts.keys())))
    start_values = [start_year_counts.get(year, 0) for year in years]
    end_values = [end_year_counts.get(year, 0) for year in years]

    charts['year_chart'] = go.Figure()
    charts['year_chart'].add_trace(go.Bar(name='开始年', x=years, y=start_values))
    charts['year_chart'].add_trace(go.Bar(name='结束年', x=years, y=end_values))
    charts['year_chart'].update_layout(
        title="项目开始年/结束年统计",
        xaxis_title="年份",
        yaxis_title="项目数量",
        barmode='group'
    )

    # 5. 按分部统计投资金额
    partment_investments = {}
    for row in panel_rows:
        partment = row[12]
        investment = safe_float_convert(row[9])
        if partment and investment > 0:
            partment_investments[partment] = partment_investments.get(partment, 0) + investment

    if partment_investments:
        charts['partment_investment_chart'] = go.Figure(data=[
            go.Bar(x=list(partment_investments.keys()), y=list(partment_investments.values()))
        ])
        charts['partment_investment_chart'].update_layout(
            title="各分部投资金额统计",
            xaxis_title="分部",
            yaxis_title="投资金额(万元)"
        )

    return charts


@ui.page("/")
def home():
    ui.add_css(r"""
    .q-item__label {
      white-space: pre;
    }
    """)

    def ui_table_panel(panel_rows: list, year: int, details: dict):
        # 获取所有可能的筛选选项
        def get_filter_options():
            """根据当前筛选状态动态获取筛选选项 - 实现特定联动关系"""
            current_state = filter_states[year]

            # 基础选项（这些不依赖其他筛选条件）
            batch_options = ["全部"] + list(set([row[4] for row in panel_rows if row[4] and str(row[4]).strip() != '']))  # 建设性质作为投资批次
            unit_options = ["全部"] + list(set([row[2] for row in panel_rows]))
            nature_options = ["全部"] + list(set([row[6] for row in panel_rows]))
            management_options = ["全部"] + list(set([row[5] for row in panel_rows]))
            partment_options = ["全部"] + list(set([row[12] for row in panel_rows]))
            start_year_options = ["全部"] + sorted(list(set([str(row[7]) for row in panel_rows if row[7] and str(row[7]).strip() != ''])))
            end_year_options = ["全部"] + sorted(list(set([str(row[8]) for row in panel_rows if row[8] and str(row[8]).strip() != ''])))

            # 负责人选项需要根据分部进行联动筛选（分部→负责人的联动关系）
            if current_state["partment"] != "全部":
                # 如果选择了特定分部，只显示该分部的负责人
                filtered_for_charge = [row for row in panel_rows if row[12] == current_state["partment"]]
                charge_options = ["全部"] + list(set([row[13] for row in filtered_for_charge if row[13] and str(row[13]).strip() != '']))
            else:
                # 如果没有选择分部，显示所有负责人
                charge_options = ["全部"] + list(set([row[13] for row in panel_rows if row[13] and str(row[13]).strip() != '']))

            return {
                "batch": batch_options,
                "unit": unit_options,
                "nature": nature_options,
                "management": management_options,
                "partment": partment_options,
                "charge": charge_options,
                "start_year": start_year_options,
                "end_year": end_year_options
            }

        
        year_str = str(year) + "年"

        with ui.tab_panel(year_str):
            # 筛选控件容器
            filter_container = ui.column().classes("mb-4 gap-2")

            # 初始化筛选选项
            initial_options = get_filter_options()

            with filter_container:
                # 第一行：基础筛选条件
                with ui.row().classes("items-center gap-4 flex-wrap"):
                    ui.label("基础筛选:").classes("text-sm font-medium")

                    batch_select = ui.select(
                        options=initial_options["batch"],
                        value=filter_states[year]["batch"],
                        label="投资批次"
                    ).classes("min-w-[120px]")

                    unit_select = ui.select(
                        options=initial_options["unit"],
                        value=filter_states[year]["unit"],
                        label="建设单位"
                    ).classes("min-w-[150px]")

                    nature_select = ui.select(
                        options=initial_options["nature"],
                        value=filter_states[year]["nature"],
                        label="项目性质"
                    ).classes("min-w-[120px]")

                    management_level = ui.select(
                        options=initial_options["management"],
                        value=filter_states[year]["level"],
                        label="管理级别"
                    ).classes("min-w-[120px]")

                    partment_select = ui.select(
                        options=initial_options["partment"],
                        value=filter_states[year]["partment"],
                        label="分部"
                    ).classes("min-w-[120px]")

                    charge_select = ui.select(
                        options=initial_options["charge"],
                        value=filter_states[year]["charge"],
                        label="负责人"
                    ).classes("min-w-[120px]")

                # 第二行：时间筛选
                with ui.row().classes("items-center gap-4 flex-wrap mt-2"):
                    ui.label("时间筛选:").classes("text-sm font-medium")

                    start_year_select = ui.select(
                        options=initial_options["start_year"],
                        value=filter_states[year]["start_year"],
                        label="开始年"
                    ).classes("min-w-[100px]")

                    end_year_select = ui.select(
                        options=initial_options["end_year"],
                        value=filter_states[year]["end_year"],
                        label="结束年"
                    ).classes("min-w-[100px]")

                # 第三行：投资范围筛选
                with ui.row().classes("items-center gap-4 flex-wrap mt-2"):
                    ui.label("投资范围(万元):").classes("text-sm font-medium")

                    investment_min = ui.number(
                        label="最小投资额",
                        value=filter_states[year]["investment_min"],
                        format="%.0f",
                        step=100
                    ).classes("min-w-[120px]")

                    ui.label("至").classes("text-sm")

                    investment_max = ui.number(
                        label="最大投资额",
                        value=filter_states[year]["investment_max"],
                        format="%.0f",
                        step=100
                    ).classes("min-w-[120px]")

                # 第四行：重置按钮和说明
                with ui.row().classes("items-center gap-4 mt-2"):
                    def reset_filters():
                        filter_states[year]["batch"] = "全部"
                        filter_states[year]["unit"] = "全部"
                        filter_states[year]["nature"] = "全部"
                        filter_states[year]["level"] = "全部"
                        filter_states[year]["partment"] = "全部"
                        filter_states[year]["charge"] = "全部"
                        filter_states[year]["start_year"] = "全部"
                        filter_states[year]["end_year"] = "全部"
                        filter_states[year]["investment_min"] = None
                        filter_states[year]["investment_max"] = None

                        # 重新获取选项并更新控件
                        new_options = get_filter_options()
                        batch_select.options = new_options["batch"]
                        unit_select.options = new_options["unit"]
                        nature_select.options = new_options["nature"]
                        management_level.options = new_options["management"]
                        partment_select.options = new_options["partment"]
                        charge_select.options = new_options["charge"]
                        start_year_select.options = new_options["start_year"]
                        end_year_select.options = new_options["end_year"]

                        batch_select.value = "全部"
                        unit_select.value = "全部"
                        nature_select.value = "全部"
                        management_level.value = "全部"
                        partment_select.value = "全部"
                        charge_select.value = "全部"
                        start_year_select.value = "全部"
                        end_year_select.value = "全部"
                        investment_min.value = None
                        investment_max.value = None

                        update_display()

                    ui.button("重置所有筛选", on_click=reset_filters).props("size=sm outline")

                    # 添加说明
                    ui.label("提示：投资金额单位为万元，留空表示不限制").classes("text-xs text-gray-500")

            # 数据显示容器
            data_container = ui.column().classes("gap-0 w-full items-stretch")

            def update_display():
                # 更新筛选状态（增加投资批次、开始年、结束年）
                filter_states[year]["batch"] = batch_select.value
                filter_states[year]["unit"] = unit_select.value
                filter_states[year]["nature"] = nature_select.value
                filter_states[year]["level"] = management_level.value
                filter_states[year]["partment"] = partment_select.value
                filter_states[year]["charge"] = charge_select.value
                filter_states[year]["start_year"] = start_year_select.value
                filter_states[year]["end_year"] = end_year_select.value
                filter_states[year]["investment_min"] = investment_min.value
                filter_states[year]["investment_max"] = investment_max.value

                # 过滤数据 - 这里是所有筛选后的数据
                filtered_data = filter_rows(panel_rows, year)

                # 计算统计信息 - 基于所有筛选后的数据
                stats = calculate_statistics(filtered_data, details, year)

                # 清空并重新创建显示内容
                data_container.clear()

                with data_container:
                    # 显示统计信息
                    with ui.card().classes("w-full mb-4"):
                        ui.label("统计信息").classes("text-lg font-bold mb-2")
                        with ui.grid(columns=4).classes("gap-4"):
                            with ui.card().classes("p-3"):
                                ui.label("项目数量").classes("text-sm text-gray-600")
                                ui.label(f"{stats['项目数量']} 个").classes("text-xl font-bold text-blue-600")

                            with ui.card().classes("p-3"):
                                ui.label("总投资合计").classes("text-sm text-gray-600")
                                ui.label(format_currency(stats['总投资合计'])).classes("text-xl font-bold text-green-600")

                            with ui.card().classes("p-3"):
                                ui.label("标段数量").classes("text-sm text-gray-600")
                                ui.label(f"{stats['标段数量']} 个").classes("text-xl font-bold text-purple-600")

                            with ui.card().classes("p-3"):
                                ui.label("合同总金额").classes("text-sm text-gray-600")
                                ui.label(format_currency(stats['合同总金额'])).classes("text-xl font-bold text-orange-600")

                        with ui.grid(columns=4).classes("gap-4 mt-2"):
                            with ui.card().classes("p-3"):
                                ui.label("资本性投资").classes("text-sm text-gray-600")
                                ui.label(format_currency(stats['总投资资本性'])).classes("text-lg font-semibold text-indigo-600")

                            with ui.card().classes("p-3"):
                                ui.label("费用性投资").classes("text-sm text-gray-600")
                                ui.label(format_currency(stats['总投资费用性'])).classes("text-lg font-semibold text-pink-600")

                            with ui.card().classes("p-3"):
                                ui.label("已支付金额").classes("text-sm text-gray-600")
                                ui.label(format_currency(stats['已支付金额'])).classes("text-lg font-semibold text-green-500")

                            with ui.card().classes("p-3"):
                                ui.label("未支付金额").classes("text-sm text-gray-600")
                                ui.label(format_currency(stats['未支付金额'])).classes("text-lg font-semibold text-red-500")

                    ui.separator()
                    ui.label(f"项目列表 (共 {len(filtered_data)} 个项目)").classes("text-lg font-medium mb-2")

                    # 分页处理
                    page_size = 20
                    total_pages = (len(filtered_data) + page_size - 1) // page_size if filtered_data else 1
                    current_page = 0

                    # 创建分页容器
                    pagination_container = ui.column().classes("w-full")
                    content_container = ui.column().classes("w-full")

                    def show_page(page_num):
                        nonlocal current_page
                        current_page = page_num

                        # 清空内容容器
                        content_container.clear()

                        with content_container:
                            start_idx = page_num * page_size
                            end_idx = min(start_idx + page_size, len(filtered_data))
                            page_data = filtered_data[start_idx:end_idx]

                            if page_data:
                                # 表头
                                with ui.grid(columns=column_frs).classes("place-items-center px-[16px] gap-2"):
                                    for col in ffill_col:
                                        ui.label(col)

                                # 数据行
                                for row in page_data:
                                    with ui.expansion().classes("border-b-2 border-gray-300 w-[2000px]").props("dense") as expan_item:
                                        with expan_item.add_slot("header"):
                                            with ui.grid(columns=column_frs).classes("gap-2 items-center"):
                                                for j in row:
                                                    ui.badge(j)

                                        ui.table(
                                            rows=details[row],
                                            columns=[
                                                {"name": "name", "field": col, "label": col}
                                                for col in desired_cols
                                                if col in dd1.columns
                                            ],
                                        )
                            else:
                                ui.label("没有找到匹配的数据").classes("text-gray-500 text-center py-8")

                        # 更新分页控件
                        update_pagination()

                    def update_pagination():
                        pagination_container.clear()

                        if total_pages > 1:
                            with pagination_container:
                                with ui.row().classes("justify-center items-center gap-2 mt-4"):
                                    # 上一页按钮
                                    ui.button("上一页",
                                              on_click=lambda: show_page(max(0, current_page - 1)),
                                              ).props("size=sm").set_enabled(current_page > 0)

                                    # 页码按钮
                                    start_page = max(0, current_page - 2)
                                    end_page = min(total_pages, start_page + 5)

                                    if start_page > 0:
                                        ui.button("1", on_click=lambda: show_page(0)).props("size=sm outline")
                                        if start_page > 1:
                                            ui.label("...").classes("px-2")

                                    for i in range(start_page, end_page):
                                        if i == current_page:
                                            ui.button(str(i + 1)).props("size=sm color=primary")
                                        else:
                                            ui.button(str(i + 1),
                                                      on_click=lambda p=i: show_page(p)
                                                      ).props("size=sm outline")

                                    if end_page < total_pages:
                                        if end_page < total_pages - 1:
                                            ui.label("...").classes("px-2")
                                        ui.button(str(total_pages),
                                                  on_click=lambda: show_page(total_pages - 1)
                                                  ).props("size=sm outline")

                                    # 下一页按钮
                                    ui.button("下一页",
                                              on_click=lambda: show_page(min(total_pages - 1, current_page + 1))
                                              ).props("size=sm").set_enabled(current_page < total_pages - 1)

                                    # 页面信息
                                    ui.label(f"第 {current_page + 1} 页，共 {total_pages} 页").classes(
                                        "text-sm text-gray-600 ml-4")

                    # 初始显示第一页
                    show_page(0)

            def update_filter_options_general():
                """更新一般筛选选项（不涉及联动）- 增加投资批次、时间筛选"""
                # 更新筛选状态
                filter_states[year]["batch"] = batch_select.value
                filter_states[year]["unit"] = unit_select.value
                filter_states[year]["nature"] = nature_select.value
                filter_states[year]["level"] = management_level.value
                filter_states[year]["charge"] = charge_select.value
                filter_states[year]["start_year"] = start_year_select.value
                filter_states[year]["end_year"] = end_year_select.value
                filter_states[year]["investment_min"] = investment_min.value
                filter_states[year]["investment_max"] = investment_max.value

                # 直接更新显示
                update_display()

            def update_partment_and_charge():
                """更新分部选择，并联动更新负责人选项（分部→负责人联动）"""
                # 更新分部筛选状态
                filter_states[year]["partment"] = partment_select.value

                # 获取新的负责人选项（基于选择的分部）
                new_options = get_filter_options()

                # 保存当前负责人选择
                old_charge_value = charge_select.value

                # 更新负责人选项
                charge_select.options = new_options["charge"]

                # 检查之前选择的负责人是否还在新选项中
                if old_charge_value not in new_options["charge"]:
                    # 如果不在，重置为"全部"
                    charge_select.value = "全部"
                    filter_states[year]["charge"] = "全部"
                else:
                    # 如果还在，保持原选择
                    charge_select.value = old_charge_value
                    filter_states[year]["charge"] = old_charge_value

                # 更新显示
                update_display()

            # 绑定筛选事件 - 增加投资批次、时间筛选，保持分部→负责人联动
            # 一般筛选条件（不涉及联动）
            batch_select.on_value_change(lambda: update_filter_options_general())
            unit_select.on_value_change(lambda: update_filter_options_general())
            nature_select.on_value_change(lambda: update_filter_options_general())
            management_level.on_value_change(lambda: update_filter_options_general())

            # 分部选择会联动影响负责人选项（分部→负责人的联动关系）
            partment_select.on_value_change(lambda: update_partment_and_charge())

            # 负责人选择只需要更新显示
            charge_select.on_value_change(lambda: update_filter_options_general())

            # 时间筛选
            start_year_select.on_value_change(lambda: update_filter_options_general())
            end_year_select.on_value_change(lambda: update_filter_options_general())

            # 投资范围筛选
            investment_min.on_value_change(lambda: update_filter_options_general())
            investment_max.on_value_change(lambda: update_filter_options_general())

            # 初始显示
            update_display()

    def create_visualization_tab():
        """创建可视化统计tab页"""
        with ui.tab_panel("可视化统计"):
            ui.label("数据可视化统计").classes("text-2xl font-bold mb-4")

            # 年份选择
            year_select = ui.select(
                options=[2023, 2024, 2025],
                value=2025,
                label="选择年份"
            ).classes("mb-4")

            # 图表容器
            charts_container = ui.column().classes("w-full gap-4")

            def update_charts():
                year = year_select.value
                if year == 2025:
                    panel_rows = panel_rows_2025
                    details = details2025
                elif year == 2024:
                    panel_rows = panel_rows_2024
                    details = details2024
                else:
                    panel_rows = panel_rows_2023
                    details = details2023

                # 清空容器
                charts_container.clear()

                # 创建图表
                charts = create_visualization_charts(panel_rows, details, year)

                with charts_container:
                    # 第一行：项目数量统计
                    with ui.row().classes("w-full gap-4"):
                        with ui.card().classes("flex-1"):
                            ui.plotly(charts['unit_chart']).classes("w-full h-96")

                        if 'nature_chart' in charts:
                            with ui.card().classes("flex-1"):
                                ui.plotly(charts['nature_chart']).classes("w-full h-96")

                    # 第二行：投资金额和年度统计
                    with ui.row().classes("w-full gap-4 mt-4"):
                        if 'investment_chart' in charts:
                            with ui.card().classes("flex-1"):
                                ui.plotly(charts['investment_chart']).classes("w-full h-96")

                        if 'year_chart' in charts:
                            with ui.card().classes("flex-1"):
                                ui.plotly(charts['year_chart']).classes("w-full h-96")

                    # 第三行：分部投资统计
                    if 'partment_investment_chart' in charts:
                        with ui.row().classes("w-full gap-4 mt-4"):
                            with ui.card().classes("w-full"):
                                ui.plotly(charts['partment_investment_chart']).classes("w-full h-96")

                    # 数据摘要
                    with ui.card().classes("w-full mt-4"):
                        ui.label("数据摘要").classes("text-lg font-bold mb-2")
                        total_projects = len(panel_rows)
                        total_investment = sum(safe_float_convert(row[9]) for row in panel_rows)

                        with ui.grid(columns=4).classes("gap-4"):
                            ui.label(f"总项目数: {total_projects}").classes("text-lg")
                            ui.label(f"总投资: {format_currency(total_investment)}").classes("text-lg")
                            ui.label(f"平均投资: {format_currency(total_investment/total_projects if total_projects > 0 else 0)}").classes("text-lg")
                            ui.label(f"数据年份: {year}年").classes("text-lg")

            # 绑定年份选择事件
            year_select.on_value_change(lambda: update_charts())

            # 初始加载
            update_charts()

    with ui.tabs() as tabs:
        ui.tab("2023年")
        ui.tab("2024年")
        ui.tab("2025年")
        ui.tab("可视化统计")

    with ui.tab_panels(tabs, value="2025年").classes("w-full"):
        ui_table_panel(panel_rows_2023, 2023, details2023)
        ui_table_panel(panel_rows_2024, 2024, details2024)
        ui_table_panel(panel_rows_2025, 2025, details2025)
        create_visualization_tab()


ui.run()
