#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版文件匹配系统 - 专注于核心功能和简单的Excel保存
"""

import os
import warnings
import time
import re
import pandas as pd
import numpy as np
from collections import defaultdict
from functools import lru_cache
from numba import njit
from concurrent.futures import ThreadPoolExecutor
import gc

warnings.filterwarnings('ignore')

class SimpleFileMatching:
    def __init__(self):
        self.start_time = time.time()
        
        # 文件路径配置
        self.p1 = '/Users/<USER>/Desktop/2025年项目资料/信息化项目里程碑及交付物标准 20240801_v4.xlsx'
        self.fileDF = '/Users/<USER>/Desktop/2025年项目资料/file0616.xlsx'
        self.profile = '/Users/<USER>/Documents/近三年投资计划/2018年至今项目清单（信息化项目）.xlsx'
        self.htp = '/Users/<USER>/Documents/科室项目管理周报/合同导数数据/合同查询导出_20250610143721870.xlsx'
        
        # 预编译正则表达式
        self.punctuation_pattern = re.compile(r'[，。、；：""''（）《》【】—…·～！？　 \n\r\t()\[\]{}!@#$%^&*\-_=+|\\:;"\'<>,.?0-9`~·！￥…（）《》【】——]+')
        
    def log_time(self, step_name: str):
        """记录执行时间"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        print(f"{step_name}: {elapsed:.2f} 秒")
        self.start_time = current_time

    @lru_cache(maxsize=1000)
    def clean_text(self, text: str) -> str:
        """缓存版本的文本清理"""
        if not text or pd.isna(text):
            return ""
        return self.punctuation_pattern.sub('', str(text))

    def load_data(self):
        """加载数据"""
        print("开始加载数据...")
        
        # 1. 加载标准数据
        type2biaozhun = {}
        try:
            s1 = pd.read_excel(self.p1, sheet_name='1_信息系统建设与升级改造', skiprows=[0], engine='calamine')['交付物名称'].dropna().tolist()
            s2 = pd.read_excel(self.p1, sheet_name='4_运维维护', skiprows=[0], engine='calamine')['交付物名称'].dropna().tolist()
            s5 = pd.read_excel(self.p1, sheet_name='5_信息专题研究项目', skiprows=[0], engine='calamine')['交付物名称'].dropna().tolist()
            
            type2biaozhun = {
                '信息化': s1,
                '信息维护修理': s2,
                '数据资产维护': s2,
                '专题研究': s5
            }
        except Exception as e:
            print(f"加载标准数据失败: {e}")
        
        self.log_time("标准数据加载")
        
        # 2. 加载项目数据
        ptypeall = pd.DataFrame()
        pronameall = []
        
        try:
            yearss = ['2022年年初', '2022年年中', '2023年年初', '2023年年中', '2024年年初', '2024年年中', '2025年年初']
            ptype1 = pd.read_excel(self.profile, sheet_name='历年投资计划', engine='calamine')
            ptype1 = ptype1[ptype1['投资年份'].isin(yearss)][['项目名称', '项目编码', '类型', '分部', '项目负责人']]
            
            ptype2 = pd.read_excel(self.profile, sheet_name='历年信息维护修理', engine='calamine')
            ptype2 = ptype2.rename(columns={'合同名称': '项目名称', '合同编号': '项目编码'})[['项目名称', '项目编码', '类型', '分部', '项目负责人']].dropna(subset=['项目名称'])
            
            ptypeall = pd.concat([ptype1, ptype2], axis=0, ignore_index=True)
            pronameall = ptypeall['项目名称'].dropna().tolist()
            
        except Exception as e:
            print(f"加载项目数据失败: {e}")
        
        self.log_time("项目数据加载")
        
        # 3. 加载主文件数据
        data00 = pd.read_excel(self.fileDF, sheet_name='2025-06-13', engine='calamine')
        self.log_time("主文件数据加载")
        
        return type2biaozhun, ptypeall, pronameall, data00

    @njit
    def fast_lcs_length(self, a, b):
        """快速LCS长度计算"""
        m, n = len(a), len(b)
        if m == 0 or n == 0:
            return 0
        
        prev = np.zeros(n + 1, dtype=np.int32)
        for i in range(m):
            curr = np.zeros(n + 1, dtype=np.int32)
            for j in range(n):
                if a[i] == b[j]:
                    curr[j + 1] = prev[j] + 1
                else:
                    curr[j + 1] = max(prev[j + 1], curr[j])
            prev = curr
        return prev[n]

    def calculate_match_score(self, text1: str, text2: str) -> int:
        """计算匹配分数"""
        if not text1 or not text2:
            return 0
        try:
            a_bytes = np.frombuffer(text1.encode('utf-8'), dtype=np.uint8)
            b_bytes = np.frombuffer(text2.encode('utf-8'), dtype=np.uint8)
            return self.fast_lcs_length(a_bytes, b_bytes)
        except:
            return 0

    def process_data(self, data00, ptypeall, pronameall):
        """处理数据"""
        print("开始处理数据...")
        
        # 预处理文件路径
        data00['文件路径'] = data00['文件路径'].str.replace(r'^L:\\审计提资项目资料\\', '', regex=True)
        data00['文件名'] = data00['文件路径'].str.rsplit('\\', n=1).str.get(-1)
        
        # 初始化新列
        data00['项目名称'] = ''
        
        # 项目名称匹配
        for project_name in pronameall:
            if project_name:
                mask = data00['文件路径'].str.contains(re.escape(str(project_name)), na=False)
                empty_mask = data00['项目名称'] == ''
                data00.loc[mask & empty_mask, '项目名称'] = str(project_name)
        
        # 合并项目信息
        if not ptypeall.empty:
            result_data = pd.merge(data00, ptypeall, on='项目名称', how='left')
        else:
            result_data = data00.copy()
            result_data['类型'] = ''
        
        result_data = result_data.drop_duplicates('文件路径').fillna('')
        self.log_time("数据处理完成")
        return result_data

    def match_files(self, standards, file_list):
        """匹配文件"""
        if not standards or not file_list:
            return {}, file_list
        
        standards_cleaned = [self.clean_text(std) for std in standards]
        results = defaultdict(list)
        no_matches = []
        
        for project_name, file_name in file_list:
            if not file_name:
                no_matches.append((project_name, file_name))
                continue
            
            # 简单分割文件名
            text_parts = str(file_name).replace('-', ' ').replace('_', ' ').replace('（', ' ').replace('）', ' ').split()
            
            best_score = 0
            best_standard_idx = -1
            
            for part in text_parts:
                part_clean = self.clean_text(part)
                if len(part_clean) < 2:
                    continue
                
                for idx, standard_clean in enumerate(standards_cleaned):
                    if len(standard_clean) < 2:
                        continue
                    
                    score = self.calculate_match_score(standard_clean, part_clean)
                    if score > best_score:
                        best_score = score
                        best_standard_idx = idx
            
            if best_score > 1:
                results[standards[best_standard_idx]].append((str(file_name), str(project_name)))
            else:
                no_matches.append((project_name, file_name))
        
        return dict(results), no_matches

    def run_matching(self):
        """运行匹配流程"""
        print("=== 启动简化版文件匹配系统 ===")
        total_start = time.time()
        
        # 1. 加载数据
        type2biaozhun, ptypeall, pronameall, data00 = self.load_data()
        
        # 2. 处理数据
        processed_data = self.process_data(data00, ptypeall, pronameall)
        
        # 3. 执行匹配
        print("开始执行文件匹配...")
        all_matches = {}
        all_no_matches = []
        
        for match_type, standards in type2biaozhun.items():
            type_data = processed_data[processed_data['类型'] == match_type]
            if type_data.empty:
                continue
            
            print(f"处理类型 '{match_type}': {len(type_data)} 个文件")
            
            file_list = [(row['项目名称'], row['文件名']) for _, row in type_data.iterrows()]
            matches, no_matches = self.match_files(standards, file_list)
            
            for standard, match_list in matches.items():
                if standard not in all_matches:
                    all_matches[standard] = []
                all_matches[standard].extend(match_list)
            
            all_no_matches.extend(no_matches)
        
        self.log_time("匹配执行完成")
        
        # 4. 生成结果DataFrame
        match_records = []
        for standard, matches in all_matches.items():
            for filename, project in matches:
                match_records.append({
                    '交付物标准': standard,
                    '文件名': filename,
                    '项目名称': project
                })
        
        # 5. 保存结果
        self.save_to_excel(processed_data, match_records, all_no_matches)
        
        # 6. 统计信息
        total_time = time.time() - total_start
        print(f"\n=== 执行统计 ===")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"处理文件: {len(processed_data)} 个")
        print(f"匹配成功: {len(match_records)} 个")
        print(f"未匹配: {len(all_no_matches)} 个")
        
        return processed_data, match_records

    def save_to_excel(self, processed_data, match_records, no_matches):
        """保存结果到Excel - 最简化版本"""
        print("保存结果到Excel...")
        
        try:
            # 转换为DataFrame
            match_df = pd.DataFrame(match_records) if match_records else pd.DataFrame()
            nomatch_df = pd.DataFrame(no_matches, columns=['项目名称', '文件名']) if no_matches else pd.DataFrame()
            
            # 保存到Excel
            output_file = '匹配结果.xlsx'
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                if not match_df.empty:
                    match_df.to_excel(writer, sheet_name='匹配结果', index=False)
                
                processed_data.to_excel(writer, sheet_name='处理数据', index=False)
                
                if not nomatch_df.empty:
                    nomatch_df.to_excel(writer, sheet_name='未匹配', index=False)
            
            print(f"✅ 结果已保存到: {output_file}")
            
        except Exception as e:
            print(f"❌ Excel保存失败: {e}")
            # 备选：保存为CSV
            try:
                if match_records:
                    pd.DataFrame(match_records).to_csv('匹配结果.csv', index=False, encoding='utf-8-sig')
                processed_data.to_csv('处理数据.csv', index=False, encoding='utf-8-sig')
                print("✅ 已保存为CSV格式")
            except Exception as csv_error:
                print(f"❌ CSV保存也失败: {csv_error}")


def main():
    """主函数"""
    matcher = SimpleFileMatching()
    try:
        result = matcher.run_matching()
        print("✅ 匹配完成!")
        return result
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    result = main()
    gc.collect()
