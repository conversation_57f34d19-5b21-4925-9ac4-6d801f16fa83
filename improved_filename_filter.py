import pandas as pd
from openai import OpenAI
import os
import json
import time
from tqdm import tqdm

# 读取数据路径
p1 = '/Users/<USER>/Desktop/2025年项目资料/file0607.xlsx'
p2 = '/Users/<USER>/Documents/近三年投资计划/2018年至今项目清单（信息化项目）.xlsx'

# 读取文件名和项目名称列表
filenames = pd.read_excel(p1, sheet_name='2025-06-05', engine='calamine')['文件名'].astype('str').tolist()
pronames1 = pd.read_excel(p2, sheet_name='历年投资计划', engine='calamine')['项目名称'].astype('str').tolist()
pronames2 = pd.read_excel(p2, sheet_name='历年信息维护修理', engine='calamine')['合同名称'].astype('str').tolist()
pronames3 = pronames1 + pronames2

print(f"加载了 {len(filenames)} 个文件名")
print(f"加载了 {len(pronames3)} 个项目关键词")

# 设置批处理参数
batch_size = 15  # 减少批次大小以获得更好的处理效果
batches = [filenames[i:i + batch_size] for i in range(0, len(filenames), batch_size)]

client = OpenAI(
    api_key="sk-e1bfa2386d234bdf9498f84804075cce",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

def create_filter_prompt(file_batch, keywords):
    """创建关键词过滤提示词"""
    # 将关键词列表转换为字符串，限制长度避免token过多
    keywords_str = "\n".join(keywords[:200])  # 限制关键词数量
    
    prompt = f"""你是一个文件名处理专家。请对以下文件名进行关键词过滤处理。

任务：根据项目关键词列表，从每个文件名中删除匹配的关键词部分，保留剩余的文件名。

项目关键词列表（前200个）：
{keywords_str}

文件名列表：
{chr(10).join([f"{i+1}. {filename}" for i, filename in enumerate(file_batch)])}

处理规则：
1. 对每个文件名进行分析，识别其中包含的项目关键词（支持模糊匹配、部分匹配）
2. 删除匹配的关键词部分，保留剩余的有意义的文件名部分
3. 如果文件名中没有匹配的关键词，保持原文件名不变
4. 保持文件扩展名不变

请返回JSON格式的结果：
{{
    "results": [
        {{
            "original_filename": "原始文件名",
            "matched_keywords": ["匹配的关键词1", "匹配的关键词2"],
            "filtered_filename": "过滤后的文件名",
            "has_match": true/false
        }}
    ]
}}

注意：只返回JSON格式的结果，不要添加其他解释文字。"""
    
    return prompt

def parse_ai_response(response_text, original_batch):
    """解析AI响应"""
    try:
        # 尝试解析JSON
        response_data = json.loads(response_text)
        if "results" in response_data:
            return response_data["results"]
        else:
            return response_data if isinstance(response_data, list) else []
    except json.JSONDecodeError:
        # 如果JSON解析失败，尝试简单的文本解析
        print(f"JSON解析失败，尝试文本解析: {response_text[:200]}...")
        results = []
        lines = response_text.strip().split('\n')
        for i, original_filename in enumerate(original_batch):
            # 简单的文本匹配逻辑
            filtered_filename = original_filename
            for line in lines:
                if original_filename in line or any(part in line for part in original_filename.split()):
                    filtered_filename = line.strip()
                    break
            
            results.append({
                "original_filename": original_filename,
                "matched_keywords": [],
                "filtered_filename": filtered_filename,
                "has_match": False
            })
        return results

# 处理所有批次
all_results = []
total_batches = len(batches)

print(f"开始处理 {total_batches} 个批次...")

for batch_idx, batch in enumerate(tqdm(batches, desc="处理批次")):
    try:
        print(f"\n处理第 {batch_idx + 1}/{total_batches} 批次，包含 {len(batch)} 个文件名")
        
        # 创建提示词
        prompt = create_filter_prompt(batch, pronames3)
        
        # 调用API
        response = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {"role": "system", "content": "你是一个专业的文件名处理助手，擅长根据关键词过滤和清理文件名。"},
                {"role": "user", "content": prompt},
            ],
            temperature=0.1,  # 降低温度以获得更一致的结果
            max_tokens=4000,
            stream=False
        )
        
        ai_output = response.choices[0].message.content.strip()
        
        # 解析响应
        batch_results = parse_ai_response(ai_output, batch)
        
        # 添加批次信息
        for result in batch_results:
            result["batch_number"] = batch_idx + 1
            result["processing_method"] = "Qwen-Plus"
            all_results.append(result)
        
        # 显示本批次的一些结果示例
        print(f"本批次处理完成，示例结果：")
        for i, result in enumerate(batch_results[:3]):  # 显示前3个结果
            print(f"  {i+1}. 原文件名: {result['original_filename']}")
            print(f"     过滤后: {result['filtered_filename']}")
            print(f"     匹配关键词: {', '.join(result.get('matched_keywords', []))}")
            print()
        
        # 添加延迟避免API限制
        if batch_idx < total_batches - 1:
            time.sleep(1)
            
    except Exception as e:
        print(f"处理第 {batch_idx + 1} 批次时出错: {str(e)}")
        # 为失败的批次创建默认结果
        for filename in batch:
            all_results.append({
                "original_filename": filename,
                "matched_keywords": [],
                "filtered_filename": filename,
                "has_match": False,
                "batch_number": batch_idx + 1,
                "processing_method": "Error",
                "error": str(e)
            })

# 创建结果DataFrame
results_df = pd.DataFrame(all_results)

# 重新排列列的顺序
column_order = [
    "original_filename", 
    "filtered_filename", 
    "has_match", 
    "matched_keywords", 
    "batch_number", 
    "processing_method"
]

# 确保所有列都存在
for col in column_order:
    if col not in results_df.columns:
        results_df[col] = ""

results_df = results_df[column_order + [col for col in results_df.columns if col not in column_order]]

# 保存结果
output_filename = "filtered_filenames_results.xlsx"
results_df.to_excel(output_filename, index=False, engine='openpyxl')

# 同时保存为CSV以便查看
csv_filename = "filtered_filenames_results.csv"
results_df.to_csv(csv_filename, index=False, encoding='utf-8-sig')

print(f"\n=== 处理完成 ===")
print(f"总文件数: {len(all_results)}")
print(f"成功处理: {len([r for r in all_results if r.get('processing_method') != 'Error'])}")
print(f"有匹配的文件: {len([r for r in all_results if r.get('has_match')])}")
print(f"结果已保存到: {output_filename}")
print(f"CSV格式结果: {csv_filename}")

# 显示一些统计信息
print(f"\n=== 处理统计 ===")
matched_count = len([r for r in all_results if r.get('has_match')])
print(f"匹配率: {matched_count/len(all_results)*100:.1f}%")

# 显示前10个处理结果示例
print(f"\n=== 前10个处理结果示例 ===")
for i, result in enumerate(all_results[:10]):
    print(f"{i+1}. 原文件名: {result['original_filename']}")
    print(f"   过滤后: {result['filtered_filename']}")
    if result.get('matched_keywords'):
        print(f"   匹配关键词: {', '.join(result['matched_keywords'])}")
    print()

# 创建仅包含过滤后文件名的简单列表
filtered_filenames = [result['filtered_filename'] for result in all_results]
simple_df = pd.DataFrame({"过滤后的文件名": filtered_filenames})
simple_df.to_excel("simple_filtered_filenames.xlsx", index=False)
print(f"简化版结果已保存到: simple_filtered_filenames.xlsx")
