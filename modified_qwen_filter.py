import pandas as pd
from openai import OpenAI
import os
import time
from tqdm import tqdm

# 读取数据路径
p1 = '/Users/<USER>/Desktop/2025年项目资料/file0607.xlsx'
p2 = '/Users/<USER>/Documents/近三年投资计划/2018年至今项目清单（信息化项目）.xlsx'

print("正在读取数据文件...")

try:
    # 读取文件名和项目名称列表
    filenames = pd.read_excel(p1, sheet_name='2025-06-05', engine='calamine')['文件名'].astype('str').tolist()
    pronames1 = pd.read_excel(p2, sheet_name='历年投资计划', engine='calamine')['项目名称'].astype('str').tolist()
    pronames2 = pd.read_excel(p2, sheet_name='历年信息维护修理', engine='calamine')['合同名称'].astype('str').tolist()
    pronames3 = pronames1 + pronames2
    
    print(f"成功读取 {len(filenames)} 个文件名")
    print(f"成功读取 {len(pronames3)} 个项目关键词")
    
except Exception as e:
    print(f"读取文件时出错: {e}")
    print("请检查文件路径和工作表名称是否正确")
    exit(1)

# 设置批处理参数
batch_size = 20  # 减少批次大小以获得更好的处理效果
batches = [filenames[i:i + batch_size] for i in range(0, len(filenames), batch_size)]

print(f"将分 {len(batches)} 个批次处理")

# 初始化OpenAI客户端
client = OpenAI(
    api_key="sk-e1bfa2386d234bdf9498f84804075cce",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

def process_batch_for_filtering(batch, keywords):
    """处理一批文件名，进行关键词过滤"""
    # 限制关键词数量以避免token过多
    limited_keywords = keywords[:100]
    keywords_text = "\n".join([f"- {kw}" for kw in limited_keywords])
    
    batch_text = "\n".join([f"{i+1}. {filename}" for i, filename in enumerate(batch)])
    
    prompt = f"""你是一个文件名处理专家。请对以下文件名进行关键词过滤处理。

任务：根据项目关键词列表，从每个文件名中删除匹配的关键词部分，保留剩余的有意义部分。

项目关键词列表：
{keywords_text}

待处理的文件名：
{batch_text}

处理要求：
1. 分析每个文件名，识别其中包含的项目关键词（支持模糊匹配、部分匹配）
2. 删除匹配的关键词部分，保留文件名中有意义的剩余部分
3. 保持文件扩展名不变
4. 如果没有匹配的关键词，保持原文件名不变

请按以下格式返回结果：
1. 过滤后的文件名1
2. 过滤后的文件名2
...

只返回过滤后的文件名列表，每行一个，不要添加其他说明。"""
    
    try:
        response = client.chat.completions.create(
            model="qwen-max",
            messages=[
                {"role": "system", "content": "你是一个专业的文件名处理助手，擅长根据关键词过滤文件名。"},
                {"role": "user", "content": prompt},
            ],
            temperature=0.1,  # 降低温度以获得更一致的结果
            max_tokens=3000,
            stream=False
        )
        
        return response.choices[0].message.content.strip()
    
    except Exception as e:
        print(f"API调用失败: {e}")
        return None

def parse_filtered_results(ai_output, original_batch):
    """解析AI输出的过滤结果"""
    if not ai_output:
        # 如果AI输出为空，返回原文件名
        return [(filename, filename, False) for filename in original_batch]
    
    filtered_lines = []
    lines = ai_output.split('\n')
    
    for line in lines:
        line = line.strip()
        if line:
            # 移除可能的序号
            if '. ' in line:
                line = line.split('. ', 1)[1]
            filtered_lines.append(line)
    
    results = []
    for i, original_filename in enumerate(original_batch):
        if i < len(filtered_lines):
            filtered_filename = filtered_lines[i]
            has_change = filtered_filename != original_filename
        else:
            filtered_filename = original_filename
            has_change = False
        
        results.append((original_filename, filtered_filename, has_change))
    
    return results

# 处理所有批次
all_results = []
total_batches = len(batches)

print(f"开始处理 {total_batches} 个批次...")

for batch_idx, batch in enumerate(tqdm(batches, desc="处理批次")):
    print(f"\n处理第 {batch_idx + 1}/{total_batches} 批次，包含 {len(batch)} 个文件名")
    
    try:
        # 调用API处理当前批次
        ai_output = process_batch_for_filtering(batch, pronames3)
        
        # 解析结果
        batch_results = parse_filtered_results(ai_output, batch)
        
        # 保存结果
        for original, filtered, has_change in batch_results:
            all_results.append({
                "原始文件名": original,
                "过滤后文件名": filtered,
                "是否有变化": has_change,
                "分析方式": "Qwen-Max",
                "所属批次": batch_idx + 1
            })
        
        # 显示本批次的一些结果
        print("本批次处理结果示例：")
        for i, (original, filtered, has_change) in enumerate(batch_results[:3]):
            status = "✓ 已过滤" if has_change else "- 无变化"
            print(f"  {status}: {original}")
            if has_change:
                print(f"    -> {filtered}")
        
        if len(batch_results) > 3:
            changed_count = sum(1 for _, _, has_change in batch_results if has_change)
            print(f"  本批次共 {changed_count}/{len(batch_results)} 个文件名有变化")
        
        # 显示原始AI输出（用于调试）
        if ai_output:
            print(f"AI原始输出前100字符: {ai_output[:100]}...")
        
    except Exception as e:
        print(f"处理第 {batch_idx + 1} 批次时出错: {e}")
        # 为失败的批次添加原始文件名
        for filename in batch:
            all_results.append({
                "原始文件名": filename,
                "过滤后文件名": filename,
                "是否有变化": False,
                "分析方式": "处理失败",
                "所属批次": batch_idx + 1
            })
    
    # 添加延迟避免API限制
    if batch_idx < total_batches - 1:
        print("等待1秒...")
        time.sleep(1)

# 创建结果DataFrame
print("\n正在保存结果...")
output_df = pd.DataFrame(all_results)

# 保存详细结果
detailed_output = "qwen_filtered_results_detailed.xlsx"
output_df.to_excel(detailed_output, index=False, engine='openpyxl')

# 创建简化版本 - 只包含过滤后的文件名
filtered_only_df = pd.DataFrame({
    "过滤后的文件名": [result["过滤后文件名"] for result in all_results]
})
simple_output = "qwen_filtered_filenames_only.xlsx"
filtered_only_df.to_excel(simple_output, index=False, engine='openpyxl')

# 创建对比版本
comparison_df = pd.DataFrame({
    "序号": range(1, len(all_results) + 1),
    "原始文件名": [result["原始文件名"] for result in all_results],
    "过滤后文件名": [result["过滤后文件名"] for result in all_results],
    "是否有变化": [result["是否有变化"] for result in all_results]
})
comparison_output = "qwen_filename_comparison.xlsx"
comparison_df.to_excel(comparison_output, index=False, engine='openpyxl')

# 输出统计信息
print(f"\n=== 处理完成 ===")
total_files = len(all_results)
changed_files = len([r for r in all_results if r["是否有变化"]])
success_files = len([r for r in all_results if r["分析方式"] != "处理失败"])

print(f"总文件数: {total_files}")
print(f"成功处理: {success_files}")
print(f"有变化的文件: {changed_files}")
print(f"变化率: {changed_files/total_files*100:.1f}%")
print(f"成功率: {success_files/total_files*100:.1f}%")

print(f"\n=== 输出文件 ===")
print(f"详细结果: {detailed_output}")
print(f"仅过滤后文件名: {simple_output}")
print(f"对比结果: {comparison_output}")

# 显示一些有变化的示例
print(f"\n=== 处理示例（有变化的文件名，前10个） ===")
changed_results = [r for r in all_results if r["是否有变化"]][:10]

if changed_results:
    for i, result in enumerate(changed_results, 1):
        print(f"{i}. 原文件名: {result['原始文件名']}")
        print(f"   过滤后: {result['过滤后文件名']}")
        print()
else:
    print("没有文件名发生变化")
    print("可能的原因：")
    print("1. 文件名中不包含项目关键词")
    print("2. 关键词匹配策略需要调整")
    print("3. AI模型理解有偏差")

print("处理完成！请查看输出文件获取详细结果。")
