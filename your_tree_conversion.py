from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable

# 您的原始数据加载代码
pp1='/Users/<USER>/Downloads/统计表(1).xlsx'
dd1=pd.read_excel(pp1,sheet_name='2023年B类新建',skiprows=[0,2,3])
dd1项目名称 = dd1[['项目名称', '项目编码']].dropna().values.tolist()
ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]

data = [i for i in ll1项目名称]

details = {
    row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
    for row in data
}

def convert_your_data_to_tree(data, details):
    """
    将您的data和details转换为AgGrid树形数据
    
    输入:
    - data: ['项目A     编码001', '项目B     编码002', ...]
    - details: {'项目A     编码001': {'rows': [{'name': '标段名称0', 'value': '0'}, ...]}, ...}
    
    输出:
    - tree_data: AgGrid可用的树形数据
    """
    tree_data = []
    
    print(f"开始转换 {len(data)} 个项目...")
    
    for i, project in enumerate(data):
        # 1. 添加项目节点（父节点）
        project_node = {
            "项目名称": project,
            "标段名称": "",
            "项目总投资": "",
            "节点类型": "项目",
            "orgHierarchy": [project]  # AgGrid树形路径
        }
        tree_data.append(project_node)
        
        # 2. 添加标段节点（子节点）
        if project in details:
            for j, detail in enumerate(details[project]["rows"]):
                detail_node = {
                    "项目名称": "",  # 子节点不显示项目名称
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "节点类型": "标段",
                    "orgHierarchy": [project, detail["name"]]  # 父->子路径
                }
                tree_data.append(detail_node)
        
        # 进度提示
        if (i + 1) % 100 == 0:
            print(f"已处理 {i + 1} 个项目")
    
    print(f"转换完成，生成 {len(tree_data)} 条树形数据")
    return tree_data

def convert_with_enhanced_info(data, details):
    """
    增强版转换，包含更多信息
    """
    tree_data = []
    
    for project in data:
        # 解析项目名称和编码
        parts = project.split('     ')
        project_name = parts[0] if len(parts) > 0 else project
        project_code = parts[1] if len(parts) > 1 else ""
        
        # 计算项目总投资（如果需要）
        total_investment = ""
        if project in details:
            try:
                total = sum(float(detail["value"]) for detail in details[project]["rows"] if detail["value"].isdigit())
                total_investment = str(total) if total > 0 else ""
            except:
                total_investment = ""
        
        # 项目节点
        tree_data.append({
            "项目名称": project_name,
            "项目编码": project_code,
            "标段名称": "",
            "项目总投资": total_investment,
            "节点类型": "项目",
            "标段数量": len(details[project]["rows"]) if project in details else 0,
            "orgHierarchy": [project]
        })
        
        # 标段节点
        if project in details:
            for detail in details[project]["rows"]:
                tree_data.append({
                    "项目名称": "",
                    "项目编码": "",
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "节点类型": "标段",
                    "标段数量": "",
                    "orgHierarchy": [project, detail["name"]]
                })
    
    return tree_data

def test_conversion():
    """测试转换函数"""
    print("=== 测试数据转换 ===")
    
    # 显示原始数据样本
    print(f"原始data数量: {len(data)}")
    print(f"原始data前3项: {data[:3]}")
    print(f"原始details数量: {len(details)}")
    
    if data:
        first_project = data[0]
        print(f"第一个项目的details: {details.get(first_project, {})}")
    
    # 转换数据
    tree_data = convert_your_data_to_tree(data, details)
    
    # 显示转换结果
    print(f"\n转换后tree_data数量: {len(tree_data)}")
    print("前6条tree_data:")
    for i, item in enumerate(tree_data[:6]):
        print(f"  {i+1}. {item}")
    
    return tree_data

# 直接替换您的原始代码
@ui.page("/")
def home():
    # 转换数据
    tree_data = convert_your_data_to_tree(data, details)
    
    # 使用分页（保持您的原始逻辑）
    summary_data = rxui.use_pagination(tree_data, page_size=100)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=10")

        # 用AgGrid替换原来的expansion + table
        ui.aggrid({
            "columnDefs": [
                {
                    "headerName": "项目名称",
                    "field": "项目名称",
                    "width": 400,
                    "cellRenderer": "agGroupCellRenderer",
                    "cellRendererParams": {
                        "innerRenderer": lambda params: params.get('value', ''),
                        "suppressCount": True
                    }
                },
                {
                    "headerName": "标段名称",
                    "field": "标段名称",
                    "width": 200
                },
                {
                    "headerName": "项目总投资",
                    "field": "项目总投资",
                    "width": 150
                }
            ],
            "rowData": summary_data.current_source.value,
            "treeData": True,
            "animateRows": True,
            "groupDefaultExpanded": 1,  # 默认展开第一层
            "getDataPath": lambda data: data.get("orgHierarchy", []),
            "autoGroupColumnDef": {
                "headerName": "项目结构",
                "width": 300,
                "cellRendererParams": {
                    "suppressCount": True,
                    "checkbox": False
                }
            },
            "defaultColDef": {
                "resizable": True,
                "sortable": True,
                "filter": True
            },
            "rowHeight": 35
        }).classes("w-full h-[600px] border-2 border-gray-300")

@ui.page("/enhanced")
def enhanced_home():
    """增强版页面"""
    # 使用增强版转换
    tree_data = convert_with_enhanced_info(data, details)
    
    ui.label("增强版树形表格").classes("text-xl font-bold mb-4")
    
    summary_data = rxui.use_pagination(tree_data, page_size=50)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=10")

        ui.aggrid({
            "columnDefs": [
                {
                    "headerName": "项目名称",
                    "field": "项目名称",
                    "width": 250,
                    "cellRenderer": "agGroupCellRenderer"
                },
                {"headerName": "项目编码", "field": "项目编码", "width": 120},
                {"headerName": "标段名称", "field": "标段名称", "width": 150},
                {"headerName": "项目总投资", "field": "项目总投资", "width": 120},
                {"headerName": "标段数量", "field": "标段数量", "width": 100}
            ],
            "rowData": summary_data.current_source.value,
            "treeData": True,
            "animateRows": True,
            "groupDefaultExpanded": 1,
            "getDataPath": lambda data: data.get("orgHierarchy", []),
            "autoGroupColumnDef": {
                "headerName": "项目结构",
                "width": 200,
                "cellRendererParams": {"suppressCount": True}
            },
            "defaultColDef": {
                "resizable": True,
                "sortable": True,
                "filter": True
            }
        }).classes("w-full h-[600px]")

if __name__ == "__main__":
    # 测试转换
    test_conversion()
    
    # 添加导航
    with ui.header():
        ui.label("项目管理系统").classes("text-white text-lg font-bold")
        with ui.row():
            ui.link("基础版", "/").classes("text-white")
            ui.link("增强版", "/enhanced").classes("text-white ml-4")
    
    ui.run()
