import time
import json
import logging
from typing import List, Dict, Any, Optional
from openai import OpenAI
from tqdm import tqdm
import pandas as pd
from config import (
    DEEPSEEK_API_KEY, 
    DEEPSEEK_BASE_URL, 
    DEFAULT_MODEL, 
    MAX_TOKENS, 
    TEMPERATURE,
    BATCH_SIZE,
    DELAY_BETWEEN_BATCHES
)

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class DeepSeekProcessor:
    """DeepSeek API批量文本处理器"""
    
    def __init__(self, api_key: Optional[str] = None, base_url: Optional[str] = None):
        """
        初始化DeepSeek处理器
        
        Args:
            api_key: DeepSeek API密钥
            base_url: API基础URL
        """
        self.api_key = api_key or DEEPSEEK_API_KEY
        self.base_url = base_url or DEEPSEEK_BASE_URL
        
        if not self.api_key:
            raise ValueError("DeepSeek API密钥未设置，请在.env文件中设置DEEPSEEK_API_KEY")
        
        self.client = OpenAI(
            api_key=self.api_key,
            base_url=self.base_url
        )
        
        logger.info("DeepSeek处理器初始化完成")
    
    def process_single_text(self, text: str, prompt: str, model: str = DEFAULT_MODEL) -> Dict[str, Any]:
        """
        处理单个文本
        
        Args:
            text: 要处理的文本
            prompt: 处理提示词
            model: 使用的模型名称
            
        Returns:
            处理结果字典
        """
        try:
            full_prompt = f"{prompt}\n\n文本：{text}"
            
            response = self.client.chat.completions.create(
                model=model,
                messages=[
                    {"role": "user", "content": full_prompt}
                ],
                max_tokens=MAX_TOKENS,
                temperature=TEMPERATURE
            )
            
            result = {
                "original_text": text,
                "processed_text": response.choices[0].message.content,
                "model": model,
                "usage": response.usage.dict() if response.usage else None,
                "status": "success"
            }
            
            return result
            
        except Exception as e:
            logger.error(f"处理文本时出错: {str(e)}")
            return {
                "original_text": text,
                "processed_text": None,
                "error": str(e),
                "status": "error"
            }
    
    def process_batch(self, texts: List[str], prompt: str, model: str = DEFAULT_MODEL) -> List[Dict[str, Any]]:
        """
        批量处理文本
        
        Args:
            texts: 文本列表
            prompt: 处理提示词
            model: 使用的模型名称
            
        Returns:
            处理结果列表
        """
        results = []
        
        for text in tqdm(texts, desc="处理文本"):
            result = self.process_single_text(text, prompt, model)
            results.append(result)
            
            # 添加延迟避免API限制
            time.sleep(0.1)
        
        return results
    
    def process_large_batch(self, texts: List[str], prompt: str, model: str = DEFAULT_MODEL) -> List[Dict[str, Any]]:
        """
        处理大批量文本，分批处理
        
        Args:
            texts: 文本列表
            prompt: 处理提示词
            model: 使用的模型名称
            
        Returns:
            处理结果列表
        """
        all_results = []
        total_batches = (len(texts) + BATCH_SIZE - 1) // BATCH_SIZE
        
        logger.info(f"开始批量处理 {len(texts)} 个文本，共 {total_batches} 批")
        
        for i in range(0, len(texts), BATCH_SIZE):
            batch_texts = texts[i:i + BATCH_SIZE]
            batch_num = i // BATCH_SIZE + 1
            
            logger.info(f"处理第 {batch_num}/{total_batches} 批，包含 {len(batch_texts)} 个文本")
            
            batch_results = self.process_batch(batch_texts, prompt, model)
            all_results.extend(batch_results)
            
            # 批次间延迟
            if i + BATCH_SIZE < len(texts):
                time.sleep(DELAY_BETWEEN_BATCHES)
        
        logger.info(f"批量处理完成，共处理 {len(all_results)} 个文本")
        return all_results
    
    def save_results(self, results: List[Dict[str, Any]], output_file: str):
        """
        保存处理结果到文件
        
        Args:
            results: 处理结果列表
            output_file: 输出文件路径
        """
        try:
            # 转换为DataFrame便于查看
            df = pd.DataFrame(results)
            
            # 保存为CSV
            csv_file = output_file.replace('.json', '.csv')
            df.to_csv(csv_file, index=False, encoding='utf-8')
            logger.info(f"结果已保存到CSV文件: {csv_file}")
            
            # 保存为JSON
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            logger.info(f"结果已保存到JSON文件: {output_file}")
            
        except Exception as e:
            logger.error(f"保存结果时出错: {str(e)}")
    
    def get_processing_stats(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        获取处理统计信息
        
        Args:
            results: 处理结果列表
            
        Returns:
            统计信息字典
        """
        total = len(results)
        successful = sum(1 for r in results if r.get('status') == 'success')
        failed = total - successful
        
        total_tokens = 0
        for result in results:
            if result.get('usage'):
                total_tokens += result['usage'].get('total_tokens', 0)
        
        stats = {
            "total_texts": total,
            "successful": successful,
            "failed": failed,
            "success_rate": successful / total if total > 0 else 0,
            "total_tokens": total_tokens
        }
        
        return stats 