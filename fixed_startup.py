from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable

# 数据加载部分
try:
    pp1='/Users/<USER>/Downloads/统计表(1).xlsx'
    dd1=pd.read_excel(pp1,sheet_name='2023年B类新建',skiprows=[0,2,3])
    dd1项目名称 = dd1[['项目名称', '项目编码']].dropna().values.tolist()
    ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]

    data = [i for i in ll1项目名称]

    details = {
            row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
            for row in data
        }
    
    print(f"✅ 数据加载成功: {len(data)} 个项目")
    
except Exception as e:
    print(f"❌ 数据加载失败: {e}")
    # 使用示例数据
    data = ["示例项目A     编码001", "示例项目B     编码002", "示例项目C     编码003"]
    details = {
        row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
        for row in data
    }
    print(f"使用示例数据: {len(data)} 个项目")

# 添加主页面路由 - 这是关键！
@ui.page("/")
def home():
    """主页面 - 使用expansion组件"""
    ui.label("项目管理系统").classes("text-2xl font-bold mb-4")
    ui.label("点击项目可以展开查看详情").classes("text-gray-600 mb-4")
    
    # 分页处理
    summary_data = rxui.use_pagination(data, page_size=20)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=5")

        with ui.column().classes("gap-2 w-full"):
            for project in summary_data.current_source.value:
                with ui.expansion(text=f"📁 {project}").classes(
                    "border border-gray-200 rounded-lg"
                ).props("dense"):
                    if project in details:
                        ui.table(
                            rows=details[project]["rows"],
                            columns=[
                                {"name": "name", "field": "name", "label": "标段名称", "align": "left"},
                                {"name": "value", "field": "value", "label": "项目总投资", "align": "right"},
                            ],
                        ).classes("w-full")
                    else:
                        ui.label("暂无标段信息").classes("text-gray-500 p-4")

@ui.page("/aggrid")
def aggrid_version():
    """AgGrid版本 - 带展开控制"""
    
    ui.label("AgGrid可展开版本").classes("text-xl font-bold mb-4")
    
    # 展开状态
    expanded_state = ui.state({project: False for project in data})
    
    def get_display_data():
        """根据展开状态生成显示数据"""
        display_data = []
        
        for project in data:
            is_expanded = expanded_state.value.get(project, False)
            icon = "📂" if is_expanded else "📁"
            
            # 项目行
            display_data.append({
                "id": f"project_{project}",
                "项目名称": f"{icon} {project}",
                "标段名称": "",
                "项目总投资": "",
                "type": "project",
                "project_key": project
            })
            
            # 标段行（只有展开时显示）
            if is_expanded and project in details:
                for detail in details[project]["rows"]:
                    display_data.append({
                        "id": f"detail_{project}_{detail['name']}",
                        "项目名称": f"　　📄 {detail['name']}",
                        "标段名称": detail["name"],
                        "项目总投资": detail["value"],
                        "type": "detail",
                        "project_key": project
                    })
        
        return display_data
    
    # 初始数据
    display_data = get_display_data()
    summary_data = rxui.use_pagination(display_data, page_size=100)
    
    # 控制按钮
    with ui.row().classes("mb-4"):
        ui.button("全部展开", on_click=lambda: toggle_all(True)).classes("bg-green-500 text-white")
        ui.button("全部折叠", on_click=lambda: toggle_all(False)).classes("bg-red-500 text-white")
    
    def toggle_all(expand):
        new_state = {project: expand for project in data}
        expanded_state.value = new_state
        refresh_display()
    
    def refresh_display():
        new_data = get_display_data()
        summary_data.set_source(new_data)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=10")
        
        def handle_cell_click(event):
            """处理单元格点击"""
            if event and 'data' in event:
                row_data = event['data']
                if row_data.get('type') == 'project':
                    project_key = row_data.get('project_key')
                    if project_key:
                        # 切换展开状态
                        current_state = expanded_state.value.copy()
                        current_state[project_key] = not current_state[project_key]
                        expanded_state.value = current_state
                        
                        refresh_display()
                        
                        status = "展开" if current_state[project_key] else "折叠"
                        ui.notify(f"{status}: {project_key}")

        ui.aggrid({
            "columnDefs": [
                {
                    "headerName": "项目名称",
                    "field": "项目名称",
                    "width": 400
                },
                {
                    "headerName": "标段名称",
                    "field": "标段名称",
                    "width": 200
                },
                {
                    "headerName": "项目总投资",
                    "field": "项目总投资",
                    "width": 200
                }
            ],
            "rowData": summary_data.current_source.value,
            "defaultColDef": {
                "resizable": True,
                "sortable": True,
                "filter": True
            },
            "rowHeight": 40,
            "suppressRowHoverHighlight": False,
            "rowSelection": "single",
            "onCellClicked": handle_cell_click
        }).classes("w-full h-[500px] border-2 border-gray-300")

@ui.page("/simple")
def simple_version():
    """简单版本 - 无分页"""
    ui.label("简单版本").classes("text-xl font-bold mb-4")
    
    with ui.column().classes("gap-2 w-full"):
        for project in data[:5]:  # 只显示前5个项目
            with ui.expansion(text=f"📁 {project}").classes("border border-gray-200 rounded"):
                if project in details:
                    for detail in details[project]["rows"]:
                        with ui.row().classes("items-center gap-4 p-2"):
                            ui.label(f"📄 {detail['name']}").classes("flex-grow")
                            ui.label(detail["value"]).classes("text-right")

@ui.page("/debug")
def debug_page():
    """调试页面"""
    ui.label("调试信息").classes("text-xl font-bold mb-4")
    
    with ui.column().classes("gap-4"):
        ui.label(f"数据项目数: {len(data)}")
        ui.label(f"详情数据数: {len(details)}")
        
        if data:
            ui.label("前3个项目:")
            for i, project in enumerate(data[:3]):
                ui.label(f"  {i+1}. {project}")
        
        ui.label("系统信息:")
        ui.label(f"  Python版本: {pd.__version__}")

if __name__ == "__main__":
    print("启动应用...")
    
    # 添加导航
    with ui.header():
        ui.label("项目管理系统").classes("text-white text-lg font-bold")
        with ui.row():
            ui.link("主页", "/").classes("text-white")
            ui.link("AgGrid版", "/aggrid").classes("text-white ml-4")
            ui.link("简单版", "/simple").classes("text-white ml-4")
            ui.link("调试", "/debug").classes("text-white ml-4")
    
    try:
        ui.run(
            host='127.0.0.1',
            port=8080,
            title='项目管理系统',
            show=True
        )
    except Exception as e:
        print(f"启动失败: {e}")
        print("尝试基础启动...")
        ui.run()
