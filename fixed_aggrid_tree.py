from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable

# 您的原始数据加载代码
pp1='/Users/<USER>/Downloads/统计表(1).xlsx'
dd1=pd.read_excel(pp1,sheet_name='2023年B类新建',skiprows=[0,2,3])
dd1项目名称 = dd1[['项目名称', '项目编码']].dropna().values.tolist()
ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]

data = [i for i in ll1项目名称]

details = {
    row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
    for row in data
}

def convert_to_simple_tree_data(data, details):
    """
    转换为简单的树形数据（避免函数序列化问题）
    """
    tree_data = []
    
    for project in data:
        # 父节点 - 项目
        tree_data.append({
            "id": f"project_{len(tree_data)}",
            "项目名称": project,
            "标段名称": "",
            "项目总投资": "",
            "type": "project",
            "level": 0,
            "expanded": True
        })
        
        # 子节点 - 标段
        if project in details:
            for detail in details[project]["rows"]:
                tree_data.append({
                    "id": f"detail_{len(tree_data)}",
                    "项目名称": "",
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "type": "detail",
                    "level": 1,
                    "expanded": False
                })
    
    return tree_data

@ui.page("/")
def home():
    """修复后的主页面 - 避免函数序列化错误"""
    
    # 转换数据
    tree_data = convert_to_simple_tree_data(data, details)
    
    # 分页
    summary_data = rxui.use_pagination(tree_data, page_size=100)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=10")

        # 修复后的AgGrid配置 - 移除所有函数
        grid_options = {
            "columnDefs": [
                {
                    "headerName": "项目名称",
                    "field": "项目名称",
                    "width": 400,
                    "pinned": "left"
                },
                {
                    "headerName": "标段名称",
                    "field": "标段名称",
                    "width": 200
                },
                {
                    "headerName": "项目总投资",
                    "field": "项目总投资",
                    "width": 150
                }
            ],
            "rowData": summary_data.current_source.value,
            "defaultColDef": {
                "resizable": True,
                "sortable": True,
                "filter": True
            },
            "rowHeight": 40,
            "headerHeight": 35,
            "suppressRowHoverHighlight": False,
            "rowSelection": "single"
        }
        
        # 创建AgGrid
        ui.aggrid(grid_options).classes("w-full h-[600px] border-2 border-gray-300")

@ui.page("/simple")
def simple_page():
    """最简单的版本 - 确保没有序列化问题"""
    
    ui.label("简单树形表格").classes("text-xl font-bold mb-4")
    
    # 转换数据
    tree_data = convert_to_simple_tree_data(data, details)
    
    # 只显示前20条数据
    limited_data = tree_data[:20]
    
    # 最简单的AgGrid配置
    ui.aggrid({
        "columnDefs": [
            {"headerName": "项目名称", "field": "项目名称", "width": 300},
            {"headerName": "标段名称", "field": "标段名称", "width": 200},
            {"headerName": "项目总投资", "field": "项目总投资", "width": 150}
        ],
        "rowData": limited_data,
        "defaultColDef": {"resizable": True}
    }).classes("w-full h-96")

@ui.page("/manual-tree")
def manual_tree_page():
    """手动实现树形效果"""
    
    ui.label("手动树形表格").classes("text-xl font-bold mb-4")
    
    # 转换数据
    tree_data = convert_to_simple_tree_data(data, details)
    
    # 分页
    summary_data = rxui.use_pagination(tree_data, page_size=50)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=5")
        
        # 手动处理树形显示
        processed_data = []
        for item in summary_data.current_source.value:
            # 根据level添加缩进
            indent = "　　" * item.get("level", 0)  # 使用全角空格缩进
            
            processed_item = {
                "项目名称": indent + item["项目名称"] if item["项目名称"] else "",
                "标段名称": item["标段名称"],
                "项目总投资": item["项目总投资"],
                "type": item.get("type", ""),
                "level": item.get("level", 0)
            }
            processed_data.append(processed_item)
        
        # 简单表格配置
        ui.aggrid({
            "columnDefs": [
                {
                    "headerName": "项目名称",
                    "field": "项目名称",
                    "width": 400
                },
                {
                    "headerName": "标段名称",
                    "field": "标段名称",
                    "width": 200
                },
                {
                    "headerName": "项目总投资",
                    "field": "项目总投资",
                    "width": 150
                }
            ],
            "rowData": processed_data,
            "defaultColDef": {
                "resizable": True,
                "sortable": True
            },
            "rowHeight": 35
        }).classes("w-full h-[500px] border border-gray-300")

@ui.page("/table-only")
def table_only_page():
    """使用NiceGUI原生table"""
    
    ui.label("原生表格版本").classes("text-xl font-bold mb-4")
    
    # 转换为表格行数据
    rows = []
    for project in data[:10]:  # 只显示前10个项目
        # 项目行
        rows.append({
            "项目名称": project,
            "标段名称": "",
            "项目总投资": "",
            "type": "project"
        })
        
        # 标段行
        if project in details:
            for detail in details[project]["rows"]:
                rows.append({
                    "项目名称": "　　" + "",  # 缩进
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "type": "detail"
                })
    
    # 表格列定义
    columns = [
        {"name": "项目名称", "label": "项目名称", "field": "项目名称", "align": "left"},
        {"name": "标段名称", "label": "标段名称", "field": "标段名称", "align": "left"},
        {"name": "项目总投资", "label": "项目总投资", "field": "项目总投资", "align": "right"}
    ]
    
    # 创建表格
    ui.table(columns=columns, rows=rows).classes("w-full")

def test_data_conversion():
    """测试数据转换"""
    print("=== 测试数据转换 ===")
    
    tree_data = convert_to_simple_tree_data(data, details)
    
    print(f"原始数据: {len(data)} 个项目")
    print(f"转换后: {len(tree_data)} 条记录")
    
    # 显示前几条
    print("\n前6条转换结果:")
    for i, item in enumerate(tree_data[:6]):
        print(f"{i+1}. {item}")
    
    # 检查是否有函数
    import json
    try:
        json.dumps(tree_data[0])
        print("\n✅ 数据可以JSON序列化")
    except Exception as e:
        print(f"\n❌ JSON序列化失败: {e}")

if __name__ == "__main__":
    # 测试数据转换
    test_data_conversion()
    
    # 添加导航
    with ui.header():
        ui.label("项目管理系统").classes("text-white text-lg font-bold")
        with ui.row():
            ui.link("主页", "/").classes("text-white")
            ui.link("简单版", "/simple").classes("text-white ml-4")
            ui.link("手动树形", "/manual-tree").classes("text-white ml-4")
            ui.link("原生表格", "/table-only").classes("text-white ml-4")
    
    ui.run()
