#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek API批量文本处理程序
使用OpenAI库调用DeepSeek API进行批量文本处理
"""

import os
import sys
from typing import List, Dict, Any
from deepseek_processor import DeepSeekProcessor
from sample_data import SAMPLE_TEXTS, SAMPLE_PROMPTS


def load_texts_from_file(file_path: str) -> List[str]:
    """
    从文件加载文本列表
    
    Args:
        file_path: 文件路径
        
    Returns:
        文本列表
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            texts = [line.strip() for line in f if line.strip()]
        return texts
    except Exception as e:
        print(f"读取文件时出错: {str(e)}")
        return []


def save_texts_to_file(texts: List[str], file_path: str):
    """
    保存文本列表到文件
    
    Args:
        texts: 文本列表
        file_path: 文件路径
    """
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            for text in texts:
                f.write(text + '\n')
        print(f"文本已保存到文件: {file_path}")
    except Exception as e:
        print(f"保存文件时出错: {str(e)}")


def interactive_mode():
    """交互式模式"""
    print("=== DeepSeek API批量文本处理 ===")
    print("1. 使用示例数据")
    print("2. 从文件加载数据")
    print("3. 手动输入数据")
    
    choice = input("请选择数据来源 (1-3): ").strip()
    
    texts = []
    if choice == "1":
        texts = SAMPLE_TEXTS
        print(f"使用示例数据，共 {len(texts)} 个文本")
    elif choice == "2":
        file_path = input("请输入文件路径: ").strip()
        texts = load_texts_from_file(file_path)
        if not texts:
            print("文件为空或读取失败")
            return
    elif choice == "3":
        print("请输入文本（每行一个，输入空行结束）:")
        while True:
            text = input().strip()
            if not text:
                break
            texts.append(text)
    else:
        print("无效选择")
        return
    
    if not texts:
        print("没有文本需要处理")
        return
    
    # 选择处理类型
    print("\n可用的处理类型:")
    for i, (key, prompt) in enumerate(SAMPLE_PROMPTS.items(), 1):
        print(f"{i}. {key}: {prompt}")
    print("7. 自定义提示词")
    
    prompt_choice = input("请选择处理类型 (1-7): ").strip()
    
    if prompt_choice == "7":
        custom_prompt = input("请输入自定义提示词: ").strip()
        prompt = custom_prompt
    elif prompt_choice.isdigit() and 1 <= int(prompt_choice) <= 6:
        prompt_key = list(SAMPLE_PROMPTS.keys())[int(prompt_choice) - 1]
        prompt = SAMPLE_PROMPTS[prompt_key]
    else:
        print("无效选择")
        return
    
    # 选择输出文件名
    output_file = input("请输入输出文件名 (默认: results.json): ").strip()
    if not output_file:
        output_file = "results.json"
    
    # 开始处理
    try:
        processor = DeepSeekProcessor()
        print(f"\n开始处理 {len(texts)} 个文本...")
        
        results = processor.process_large_batch(texts, prompt)
        
        # 保存结果
        processor.save_results(results, output_file)
        
        # 显示统计信息
        stats = processor.get_processing_stats(results)
        print("\n=== 处理统计 ===")
        print(f"总文本数: {stats['total_texts']}")
        print(f"成功处理: {stats['successful']}")
        print(f"处理失败: {stats['failed']}")
        print(f"成功率: {stats['success_rate']:.2%}")
        print(f"总token数: {stats['total_tokens']}")
        
    except Exception as e:
        print(f"处理过程中出错: {str(e)}")


def batch_mode(input_file: str, output_file: str, prompt: str, model: str = "deepseek-chat"):
    """
    批处理模式
    
    Args:
        input_file: 输入文件路径
        output_file: 输出文件路径
        prompt: 处理提示词
        model: 使用的模型
    """
    try:
        # 加载文本
        texts = load_texts_from_file(input_file)
        if not texts:
            print("输入文件为空或读取失败")
            return
        
        # 初始化处理器
        processor = DeepSeekProcessor()
        print(f"开始处理 {len(texts)} 个文本...")
        
        # 批量处理
        results = processor.process_large_batch(texts, prompt, model)
        
        # 保存结果
        processor.save_results(results, output_file)
        
        # 显示统计信息
        stats = processor.get_processing_stats(results)
        print(f"处理完成！成功率: {stats['success_rate']:.2%}")
        
    except Exception as e:
        print(f"批处理过程中出错: {str(e)}")


def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 命令行模式
        if len(sys.argv) < 4:
            print("用法: python main.py <input_file> <output_file> <prompt> [model]")
            print("示例: python main.py input.txt results.json '请总结以下文本:'")
            return
        
        input_file = sys.argv[1]
        output_file = sys.argv[2]
        prompt = sys.argv[3]
        model = sys.argv[4] if len(sys.argv) > 4 else "deepseek-chat"
        
        batch_mode(input_file, output_file, prompt, model)
    else:
        # 交互式模式
        interactive_mode()


if __name__ == '__main__':
    main()

# See PyCharm help at https://www.jetbrains.com/help/pycharm/
