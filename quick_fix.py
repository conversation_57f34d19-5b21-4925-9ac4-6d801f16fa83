# 快速修复您的代码 - 移除所有导致序列化错误的函数

@ui.page("/")
def home():
    # 将数据转换为简单格式
    def prepare_tree_data():
        tree_rows = []
        for project in data:
            # 项目行（父节点）
            tree_rows.append({
                "项目名称": project,
                "标段名称": "",
                "项目总投资": "",
                "level": 0
            })
            
            # 标段行（子节点）
            if project in details:
                for detail in details[project]["rows"]:
                    tree_rows.append({
                        "项目名称": "",
                        "标段名称": detail["name"],
                        "项目总投资": detail["value"],
                        "level": 1
                    })
        return tree_rows
    
    # 准备数据
    tree_data = prepare_tree_data()
    summary_data = rxui.use_pagination(tree_data, page_size=100)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=10")

        # 修复后的AgGrid配置 - 移除所有lambda函数
        ui.aggrid({
            "columnDefs": [
                {
                    "headerName": "项目名称",
                    "field": "项目名称",
                    "width": 400
                },
                {
                    "headerName": "标段名称", 
                    "field": "标段名称",
                    "width": 200
                },
                {
                    "headerName": "项目总投资",
                    "field": "项目总投资",
                    "width": 200
                }
            ],
            "rowData": summary_data.current_source.value,
            "defaultColDef": {
                "resizable": True,
                "sortable": True,
                "filter": True
            },
            "rowHeight": 40
        }).classes("w-full h-[600px] border-2 border-gray-300")


ui.run()
