import os
from openai import OpenAI
import pandas as pd
import time
from tqdm import tqdm

# 读取数据
pp1='/Users/<USER>/Desktop/2025年项目资料/文件日期/合并后的文件.xlsx'
data1=pd.read_excel(pp1,sheet_name='Sheet1',engine='calamine')[['文件路径',1]].dropna().iloc[0:50]
print(f"加载了 {len(data1.values)} 条数据")

# 初始化OpenAI客户端
client = OpenAI(
    api_key="sk-e1bfa2386d234bdf9498f84804075cce",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)


def is_date_and_convert(text):
    """判断文本是否为日期并转换格式，确保返回最简洁的结果"""
    try:
        completion = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {
                    'role': 'system', 
                    'content': '你是日期识别专家。严格按要求返回，不要任何解释或多余文字。'
                },
                {
                    'role': 'user', 
                    'content': f'判断文本"{text}"是否为日期。\n\n规则：\n- 如果是日期：返回"YYYY年MM月DD日"格式\n- 如果不是日期：只返回"否"\n\n不要返回其他任何内容。'
                }
            ],
            temperature=0,      # 设为0获得最确定的结果
            max_tokens=20,      # 严格限制输出长度
            top_p=0.1          # 进一步限制输出的随机性
        )
        
        result = completion.choices[0].message.content.strip()
        
        # 后处理：确保结果简洁
        result = result.replace('。', '').replace('，', '').replace(',', '').replace('.', '')
        
        # 如果包含否定词汇，统一返回"否"
        negative_words = ['否', '不是', '非', '无法', '不能', '错误', '无效']
        if any(word in result for word in negative_words):
            return "否"
        
        # 如果包含年月日，提取并格式化
        if '年' in result and ('月' in result or '日' in result):
            return result
        
        # 其他情况返回原结果或"否"
        return result if len(result) <= 15 else "否"
        
    except Exception as e:
        return "处理失败"


# 处理数据
samples = data1.values.tolist()
results = []

print("开始处理...")
for fname, text in tqdm(samples, desc="日期识别"):
    converted_result = is_date_and_convert(text)
    results.append([fname, text, converted_result])
    
    # 添加延迟
    time.sleep(0.05)

# 保存结果
result_df = pd.DataFrame(results, columns=["文件路径", "原始文本", "转换结果"])
result_df.to_excel("简洁版日期结果.xlsx", index=False, engine='openpyxl')

# 输出统计
total = len(results)
dates = len([r for r in results if r[2] != "否" and r[2] != "处理失败"])
print(f"\n处理完成: {total}条数据，识别到{dates}个日期")

# 显示结果示例
print("\n结果示例:")
for i, (fname, original, result) in enumerate(results[:10]):
    print(f"{i+1}. {os.path.basename(fname)}: {original} -> {result}")

print("\n结果已保存到: 简洁版日期结果.xlsx")
