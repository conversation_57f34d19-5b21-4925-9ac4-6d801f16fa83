import os
from openai import OpenAI
import pandas as pd
import time
from tqdm import tqdm

# 读取数据
pp1 = '/Users/<USER>/Desktop/2025年项目资料/文件日期/合并后的文件.xlsx'
data1 = pd.read_excel(pp1, sheet_name='Sheet1', engine='calamine')
data11 = data1.dropna(how='all', subset=data1.columns[2:]).fillna(0)
ld11 = data11.values.tolist()

print(f"加载数据: {len(ld11)} 行 x {len(ld11[0]) if ld11 else 0} 列")

# 创建新的二维列表ld22，去掉前2个元素
ld22 = []
for row in ld11:
    if len(row) > 2:
        # 去掉前2个元素，保留从第3个元素开始的数据
        new_row = row[2:]
        ld22.append(new_row)
    else:
        # 如果行数据少于3个元素，添加空列表
        ld22.append([])

print(f"创建ld22: {len(ld22)} 行 x {len(ld22[0]) if ld22 and ld22[0] else 0} 列")

# 初始化OpenAI客户端
client = OpenAI(
    api_key="sk-e1bfa2386d234bdf9498f84804075cce",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)


def is_date_and_convert(text):
    """判断文本是否为日期并转换格式"""
    try:
        completion = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {
                    'role': 'system',
                    'content': '你是日期识别专家。严格按要求返回，不要解释。'
                },
                {
                    'role': 'user',
                    'content': f'判断"{text}"是否为日期。是日期返回"YYYY年MM月DD日"格式，不是返回"否"。'
                }
            ],
            temperature=0,
            max_tokens=20,
            top_p=0.1
        )

        result = completion.choices[0].message.content.strip()
        result = result.replace('。', '').replace('，', '').replace(',', '').replace('.', '')

        # 检查否定词
        if any(word in result for word in ['否', '不是', '非', '无法', '不能']):
            return "否"

        # 检查日期格式
        if '年' in result and ('月' in result or '日' in result):
            return result

        return result if len(result) <= 15 else "否"

    except Exception as e:
        return "处理失败"


# 处理ld22数据
results = []
processed_count = 0
skipped_count = 0

print("开始处理ld22...")

# 创建结果矩阵，用于存储识别结果
ld22_results = []

for row_idx, row in enumerate(tqdm(ld22, desc="处理数据")):
    # 获取原始ld11中的前两列作为标识
    original_row = ld11[row_idx]
    row_id = original_row[0] if len(original_row) > 0 and original_row[0] != 0 else f"行{row_idx+1}"
    row_desc = original_row[1] if len(original_row) > 1 and original_row[1] != 0 else ""

    # 为当前行创建结果列表
    row_results = []

    # 处理ld22中的每个元素
    for col_idx, value in enumerate(row):
        # 跳过0值和空值
        if value == 0 or value == '0' or pd.isna(value) or value == '':
            skipped_count += 1
            row_results.append("跳过")  # 标记为跳过
            continue

        # 处理非0值
        date_result = is_date_and_convert(str(value))
        row_results.append(date_result)  # 存储识别结果

        # 同时保存到详细结果中
        results.append([
            row_id,                      # 行标识
            row_desc,                    # 行描述
            f"第{col_idx+3}列",          # 列位置（+3因为去掉了前2列）
            str(value),                  # 原始值
            date_result                  # 识别结果
        ])

        processed_count += 1

        # 每处理100个显示进度
        if processed_count % 100 == 0:
            print(f"已处理 {processed_count} 个值")

        # API延迟
        time.sleep(0.05)

    # 将当前行的结果添加到结果矩阵
    ld22_results.append(row_results)

print(f"ld22处理完成，共处理 {processed_count} 个值，跳过 {skipped_count} 个值")

# 将结果整合回ld11
print("整合结果回ld11...")
ld11_with_results = []

for row_idx, original_row in enumerate(ld11):
    # 保留前两列
    new_row = original_row[:2].copy() if len(original_row) >= 2 else original_row.copy()

    # 添加原始数据（第3列开始）
    if len(original_row) > 2:
        new_row.extend(original_row[2:])

    # 添加识别结果
    if row_idx < len(ld22_results):
        new_row.extend(ld22_results[row_idx])

    ld11_with_results.append(new_row)

# 保存结果
if results:
    # 创建详细结果DataFrame
    df = pd.DataFrame(results, columns=["行标识", "行描述", "列位置", "原始值", "识别结果"])

    # 保存详细结果
    df.to_excel("日期识别详细结果.xlsx", index=False, engine='openpyxl')

    # 创建整合后的ld11结果DataFrame
    # 构建列名
    original_cols = [f"原始列{i+1}" for i in range(len(ld11[0]) if ld11 else 0)]
    result_cols = [f"识别结果{i+1}" for i in range(len(ld22[0]) if ld22 and ld22[0] else 0)]
    all_cols = original_cols + result_cols

    # 创建整合结果DataFrame
    integrated_df = pd.DataFrame(ld11_with_results, columns=all_cols[:len(ld11_with_results[0]) if ld11_with_results else 0])
    integrated_df.to_excel("整合后的完整结果.xlsx", index=False, engine='openpyxl')

    # 统计信息
    total = len(results)
    dates = len(df[df['识别结果'].str.contains('年', na=False)])
    non_dates = len(df[df['识别结果'] == '否'])
    failed = len(df[df['识别结果'] == '处理失败'])

    print(f"\n=== 处理完成 ===")
    print(f"原始ld11: {len(ld11)} 行 x {len(ld11[0]) if ld11 else 0} 列")
    print(f"处理ld22: {len(ld22)} 行 x {len(ld22[0]) if ld22 and ld22[0] else 0} 列")
    print(f"整合结果: {len(ld11_with_results)} 行 x {len(ld11_with_results[0]) if ld11_with_results else 0} 列")
    print(f"处理数据: {processed_count} 个")
    print(f"跳过数据: {skipped_count} 个")
    print(f"识别为日期: {dates} 个")
    print(f"非日期: {non_dates} 个")
    print(f"处理失败: {failed} 个")
    print(f"日期识别率: {dates/total*100:.1f}%")

    # 显示示例
    print(f"\n=== 详细结果示例 ===")
    for i, (_, row) in enumerate(df.head(5).iterrows()):
        print(f"{i+1}. {row['行标识']} | {row['列位置']}: {row['原始值']} -> {row['识别结果']}")

    # 显示整合结果示例
    print(f"\n=== 整合结果示例（前3行） ===")
    for i, row in enumerate(ld11_with_results[:3]):
        print(f"第{i+1}行: {row[:5]}... (显示前5列)")

    # 显示识别到的日期
    date_df = df[df['识别结果'].str.contains('年', na=False)]
    if not date_df.empty:
        print(f"\n=== 识别到的日期（前5个） ===")
        for i, (_, row) in enumerate(date_df.head(5).iterrows()):
            print(f"{i+1}. {row['行标识']} | {row['列位置']}: {row['原始值']} -> {row['识别结果']}")

        # 保存仅日期结果
        date_df.to_excel("仅日期结果.xlsx", index=False, engine='openpyxl')
        print(f"\n日期结果已单独保存到: 仅日期结果.xlsx")

    print(f"\n=== 保存的文件 ===")
    print(f"1. 日期识别详细结果.xlsx - 详细的识别过程和结果")
    print(f"2. 整合后的完整结果.xlsx - 原始数据+识别结果的完整表格")
    print(f"3. 仅日期结果.xlsx - 只包含识别为日期的数据")

else:
    print("没有数据需要处理")

print("\n程序执行完毕！")
print(f"最终的ld11_with_results变量包含了原始数据和识别结果的整合")
