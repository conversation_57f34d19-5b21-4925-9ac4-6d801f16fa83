#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件名处理器测试脚本
"""

import os
import sys
from qwen_file_processor import QwenFileProcessor


def test_single_filename():
    """测试单个文件名处理"""
    print("=== 测试单个文件名处理 ===")
    
    try:
        processor = QwenFileProcessor()
        
        # 测试数据
        keywords = ["技术", "报告", "文档"]
        filename = "技术方案设计文档v1.0.docx"
        
        print(f"关键词: {keywords}")
        print(f"文件名: {filename}")
        print("处理中...")
        
        result = processor.process_single_filename(keywords, filename)
        
        print("\n处理结果:")
        print(f"原始文件名: {result['original_filename']}")
        print(f"是否匹配: {result['has_match']}")
        print(f"匹配的关键词: {result['matched_keywords']}")
        print(f"处理后文件名: {result['remaining_filename']}")
        print(f"说明: {result['explanation']}")
        print(f"状态: {result['status']}")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")


def test_batch_processing():
    """测试批量处理"""
    print("\n=== 测试批量处理 ===")
    
    try:
        processor = QwenFileProcessor()
        
        # 测试数据
        keywords = ["技术", "项目", "系统"]
        filenames = [
            "技术方案设计文档v1.0.docx",
            "项目管理计划书.pdf",
            "系统开发进度报告.xlsx",
            "用户需求分析文档.docx"
        ]
        
        print(f"关键词: {keywords}")
        print(f"文件名数量: {len(filenames)}")
        print("处理中...")
        
        results = processor.process_filename_batch(keywords, filenames)
        
        print("\n处理结果:")
        for i, result in enumerate(results, 1):
            print(f"{i}. {result['original_filename']}")
            print(f"   匹配: {result['has_match']}")
            print(f"   关键词: {result['matched_keywords']}")
            print(f"   结果: {result['remaining_filename']}")
            print()
        
        # 保存结果
        processor.save_results(results, "test_results.json")
        
        # 显示统计
        stats = processor.get_processing_stats(results)
        print("统计信息:")
        print(f"总文件数: {stats['total_files']}")
        print(f"成功处理: {stats['successful']}")
        print(f"匹配文件数: {stats['matched_files']}")
        print(f"匹配率: {stats['match_rate']:.2%}")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")


def test_with_sample_files():
    """使用示例文件测试"""
    print("\n=== 使用示例文件测试 ===")
    
    if not os.path.exists("sample_keywords.txt") or not os.path.exists("sample_filenames.txt"):
        print("示例文件不存在，请先运行主程序创建示例文件")
        return
    
    try:
        # 读取示例数据
        with open("sample_keywords.txt", 'r', encoding='utf-8') as f:
            keywords = [line.strip() for line in f if line.strip()]
        
        with open("sample_filenames.txt", 'r', encoding='utf-8') as f:
            filenames = [line.strip() for line in f if line.strip()]
        
        print(f"加载了 {len(keywords)} 个关键词和 {len(filenames)} 个文件名")
        
        processor = QwenFileProcessor()
        
        # 只处理前5个文件名进行测试
        test_filenames = filenames[:5]
        print(f"测试处理前 {len(test_filenames)} 个文件名...")
        
        results = processor.process_filename_batch(keywords, test_filenames)
        
        print("\n测试结果:")
        for result in results:
            if result['status'] == 'success':
                print(f"原文件名: {result['original_filename']}")
                print(f"匹配关键词: {', '.join(result['matched_keywords']) if result['matched_keywords'] else '无'}")
                print(f"处理后: {result['remaining_filename']}")
                print("-" * 50)
        
        # 保存测试结果
        processor.save_results(results, "sample_test_results.json")
        print("测试结果已保存到 sample_test_results.json")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")


def main():
    """主测试函数"""
    print("Qwen文件名处理器测试")
    print("请确保已正确配置API密钥")
    
    if len(sys.argv) > 1:
        test_type = sys.argv[1]
        if test_type == "single":
            test_single_filename()
        elif test_type == "batch":
            test_batch_processing()
        elif test_type == "sample":
            test_with_sample_files()
        else:
            print("无效的测试类型，可选: single, batch, sample")
    else:
        print("选择测试类型:")
        print("1. 单个文件名测试")
        print("2. 批量处理测试")
        print("3. 示例文件测试")
        
        choice = input("请选择 (1-3): ").strip()
        
        if choice == "1":
            test_single_filename()
        elif choice == "2":
            test_batch_processing()
        elif choice == "3":
            test_with_sample_files()
        else:
            print("无效选择")


if __name__ == '__main__':
    main()
