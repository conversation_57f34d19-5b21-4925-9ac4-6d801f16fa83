from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable


def load_project_data(file_path: str = '/Users/<USER>/Downloads/统计表(1).xlsx', 
                     sheet_name: str = '2023年B类新建'):
    """
    加载项目数据
    """
    try:
        print(f"正在读取文件: {file_path}")
        dd1 = pd.read_excel(file_path, sheet_name=sheet_name, skiprows=[0, 2, 3])
        print(f"数据形状: {dd1.shape}")
        print(f"列名: {dd1.columns.tolist()}")
        
        # 检查必要的列是否存在
        required_cols = ['项目名称', '项目编码']
        available_cols = [col for col in required_cols if col in dd1.columns]
        
        if not available_cols:
            print("错误: 找不到必要的列")
            return []
        
        dd1项目名称 = dd1[available_cols].dropna().values.tolist()
        ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]
        data = [i for i in ll1项目名称]
        
        print(f"成功加载 {len(data)} 个项目")
        return data
        
    except FileNotFoundError:
        print(f"文件不存在: {file_path}")
        return []
    except Exception as e:
        print(f"加载数据失败: {e}")
        return []


def create_project_details(data):
    """
    创建项目详情数据
    """
    if not data:
        return {}
    
    details = {
        row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
        for row in data
    }
    return details


# 测试数据加载
def test_data_loading():
    """测试数据加载功能"""
    print("=== 测试数据加载 ===")
    data = load_project_data()
    details = create_project_details(data)
    
    print(f"数据条数: {len(data)}")
    print(f"详情条数: {len(details)}")
    
    if data:
        print("前3条数据:")
        for i, item in enumerate(data[:3]):
            print(f"  {i+1}. {item}")
    
    return data, details


@ui.page("/")
def home():
    """主页面"""
    
    # 页面标题
    ui.label("项目管理系统").classes("text-2xl font-bold mb-4")
    
    try:
        # 加载数据
        data = load_project_data()
        details = create_project_details(data)
        
        if not data:
            ui.label("❌ 没有加载到数据").classes("text-red-500")
            ui.label("请检查文件路径和格式").classes("text-gray-500")
            return
        
        ui.label(f"✅ 成功加载 {len(data)} 个项目").classes("text-green-600 mb-4")
        
        # 创建分页
        summary_data = rxui.use_pagination(data, page_size=10)  # 减少页面大小

        @effect_refreshable.on(summary_data)
        def on_summary_data_changed():
            # 分页控件
            summary_data.create_q_pagination().props("max-pages=5")

            # 项目列表
            with ui.column().classes("gap-2 w-full"):
                for row in summary_data.current_source.value:
                    with ui.expansion(text=row).classes("border border-gray-200 rounded"):
                        if row in details:
                            ui.table(
                                rows=details[row]["rows"],
                                columns=[
                                    {"name": "name", "field": "name", "label": "标段名称"},
                                    {"name": "value", "field": "value", "label": "项目总投资"},
                                ],
                            ).classes("w-full")
                        else:
                            ui.label("暂无详情").classes("text-gray-500 p-2")
    
    except Exception as e:
        ui.label(f"❌ 页面加载失败: {e}").classes("text-red-500")
        print(f"页面错误: {e}")


@ui.page("/test")
def test_page():
    """测试页面"""
    ui.label("数据测试页面").classes("text-xl font-bold mb-4")
    
    with ui.column().classes("gap-4"):
        ui.button("测试数据加载", on_click=lambda: test_data_loading())
        
        # 显示测试结果
        result_area = ui.textarea("测试结果").classes("w-full h-64")
        
        def run_test():
            try:
                data, details = test_data_loading()
                result_text = f"""
测试结果:
- 数据条数: {len(data)}
- 详情条数: {len(details)}

前5条数据:
"""
                for i, item in enumerate(data[:5]):
                    result_text += f"{i+1}. {item}\n"
                
                result_area.value = result_text
            except Exception as e:
                result_area.value = f"测试失败: {e}"
        
        ui.button("运行完整测试", on_click=run_test).classes("bg-blue-500 text-white")


if __name__ == "__main__":
    # 先测试数据加载
    print("启动前测试...")
    test_data_loading()
    
    # 启动应用
    try:
        ui.run(
            host='127.0.0.1',
            port=8080,
            title='项目管理系统',
            favicon='🏗️',
            dark=False,
            show=True
        )
    except Exception as e:
        print(f"启动失败: {e}")
        print("尝试基础启动...")
        ui.run()
