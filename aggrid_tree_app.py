from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable


def load_project_data():
    """加载项目数据"""
    try:
        pp1 = '/Users/<USER>/Downloads/统计表(1).xlsx'
        dd1 = pd.read_excel(pp1, sheet_name='2023年B类新建', skiprows=[0, 2, 3])
        dd1项目名称 = dd1[['项目名称', '项目编码']].dropna().values.tolist()
        ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]
        data = [i for i in ll1项目名称]
        return data
    except Exception as e:
        print(f"加载数据失败: {e}")
        return []


def create_project_details(data):
    """创建项目详情"""
    details = {
        row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
        for row in data
    }
    return details


def create_tree_data(data, details):
    """创建树形结构数据"""
    tree_data = []
    
    for i, project in enumerate(data):
        # 父节点 - 项目
        parent_node = {
            "id": f"project_{i}",
            "项目名称": project,
            "标段名称": "",
            "项目总投资": "",
            "type": "project",
            "children": []
        }
        
        # 子节点 - 标段
        if project in details:
            for j, detail in enumerate(details[project]["rows"]):
                child_node = {
                    "id": f"project_{i}_detail_{j}",
                    "项目名称": "",
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "type": "detail",
                    "parent": f"project_{i}"
                }
                parent_node["children"].append(child_node)
        
        tree_data.append(parent_node)
    
    return tree_data


def flatten_tree_data(tree_data):
    """将树形数据扁平化为aggrid可用格式"""
    flattened = []
    
    for parent in tree_data:
        # 添加父节点
        flattened.append({
            "id": parent["id"],
            "项目名称": parent["项目名称"],
            "标段名称": parent["标段名称"],
            "项目总投资": parent["项目总投资"],
            "type": parent["type"],
            "level": 0,
            "expanded": True,
            "hasChildren": len(parent.get("children", [])) > 0
        })
        
        # 添加子节点
        for child in parent.get("children", []):
            flattened.append({
                "id": child["id"],
                "项目名称": child["项目名称"],
                "标段名称": child["标段名称"],
                "项目总投资": child["项目总投资"],
                "type": child["type"],
                "level": 1,
                "parent": child["parent"],
                "expanded": False,
                "hasChildren": False
            })
    
    return flattened


# 加载数据
data = load_project_data()
details = create_project_details(data)


@ui.page("/")
def home():
    """使用aggrid树形表格的主页面"""
    
    ui.label("项目管理系统 - 树形表格").classes("text-2xl font-bold mb-4")
    
    if not data:
        ui.label("没有数据").classes("text-red-500")
        return
    
    # 创建树形数据
    tree_data = create_tree_data(data, details)
    flattened_data = flatten_tree_data(tree_data)
    
    ui.label(f"共 {len(data)} 个项目").classes("text-green-600 mb-4")
    
    # 分页数据
    summary_data = rxui.use_pagination(flattened_data, page_size=50)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        # 分页控件
        summary_data.create_q_pagination().props("max-pages=10")
        
        # AgGrid 树形表格配置
        column_defs = [
            {
                "headerName": "项目名称",
                "field": "项目名称",
                "cellRenderer": "agGroupCellRenderer",
                "cellRendererParams": {
                    "innerRenderer": lambda params: params.get('value', ''),
                    "suppressCount": True
                },
                "width": 400,
                "pinned": "left"
            },
            {
                "headerName": "标段名称", 
                "field": "标段名称",
                "width": 200
            },
            {
                "headerName": "项目总投资",
                "field": "项目总投资", 
                "width": 150
            }
        ]
        
        # 网格选项
        grid_options = {
            "columnDefs": column_defs,
            "rowData": summary_data.current_source.value,
            "treeData": True,
            "animateRows": True,
            "groupDefaultExpanded": 1,
            "getDataPath": lambda data: [data.get("项目名称", ""), data.get("标段名称", "")],
            "autoGroupColumnDef": {
                "headerName": "项目结构",
                "width": 300,
                "cellRendererParams": {
                    "suppressCount": True,
                    "checkbox": False
                }
            },
            "defaultColDef": {
                "flex": 1,
                "minWidth": 100,
                "resizable": True,
                "sortable": True,
                "filter": True
            },
            "rowSelection": "single",
            "suppressRowClickSelection": False,
            "rowHeight": 40
        }
        
        # 创建AgGrid
        ui.aggrid(grid_options).classes("w-full h-96")


@ui.page("/simple")
def simple_tree():
    """简化版树形表格"""
    
    ui.label("简化版树形表格").classes("text-xl font-bold mb-4")
    
    if not data:
        ui.label("没有数据").classes("text-red-500")
        return
    
    # 准备简单的树形数据
    rows = []
    for i, project in enumerate(data[:10]):  # 只显示前10个项目
        # 项目行
        rows.append({
            "id": i * 100,
            "项目名称": project,
            "标段名称": "",
            "项目总投资": "",
            "level": 0
        })
        
        # 标段行
        if project in details:
            for j, detail in enumerate(details[project]["rows"]):
                rows.append({
                    "id": i * 100 + j + 1,
                    "项目名称": "",
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "level": 1
                })
    
    # 简单的AgGrid配置
    ui.aggrid({
        "columnDefs": [
            {
                "headerName": "项目名称",
                "field": "项目名称",
                "width": 300,
                "cellStyle": lambda params: {
                    "paddingLeft": f"{params['data']['level'] * 20 + 10}px",
                    "fontWeight": "bold" if params['data']['level'] == 0 else "normal"
                }
            },
            {"headerName": "标段名称", "field": "标段名称", "width": 200},
            {"headerName": "项目总投资", "field": "项目总投资", "width": 150}
        ],
        "rowData": rows,
        "defaultColDef": {
            "resizable": True,
            "sortable": True,
            "filter": True
        }
    }).classes("w-full h-96")


@ui.page("/nested")
def nested_tree():
    """嵌套结构树形表格"""
    
    ui.label("嵌套结构树形表格").classes("text-xl font-bold mb-4")
    
    if not data:
        ui.label("没有数据").classes("text-red-500")
        return
    
    # 创建嵌套结构数据
    nested_data = []
    for i, project in enumerate(data[:5]):  # 只显示前5个项目
        project_node = {
            "项目名称": project,
            "标段名称": "",
            "项目总投资": "",
            "children": []
        }
        
        if project in details:
            for detail in details[project]["rows"]:
                project_node["children"].append({
                    "项目名称": "",
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"]
                })
        
        nested_data.append(project_node)
    
    # 嵌套AgGrid配置
    ui.aggrid({
        "columnDefs": [
            {
                "headerName": "项目名称",
                "field": "项目名称",
                "cellRenderer": "agGroupCellRenderer",
                "width": 300
            },
            {"headerName": "标段名称", "field": "标段名称", "width": 200},
            {"headerName": "项目总投资", "field": "项目总投资", "width": 150}
        ],
        "rowData": nested_data,
        "treeData": True,
        "animateRows": True,
        "groupDefaultExpanded": 1,
        "getDataPath": lambda data: [data.get("项目名称") or "子项目"],
        "autoGroupColumnDef": {
            "headerName": "项目层级",
            "width": 200,
            "cellRendererParams": {"suppressCount": True}
        }
    }).classes("w-full h-96")


if __name__ == "__main__":
    # 添加导航
    with ui.header():
        ui.label("项目管理系统").classes("text-white text-lg font-bold")
        with ui.row():
            ui.link("树形表格", "/").classes("text-white")
            ui.link("简化版", "/simple").classes("text-white ml-4")
            ui.link("嵌套版", "/nested").classes("text-white ml-4")
    
    ui.run()
