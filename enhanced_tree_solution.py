from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable


pp1='/Users/<USER>/Downloads/统计表(1).xlsx'
dd1=pd.read_excel(pp1,sheet_name='2023年B类新建',skiprows=[0,2,3])
dd1项目名称 = dd1[['项目名称', '项目编码']].dropna().values.tolist()
ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]

data = [i for i in ll1项目名称]

details = {
        row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
        for row in data
    }

def convert_to_tree_data_v1(data, details):
    """方案1: 使用图标和缩进"""
    tree_data = []
    for project in data:
        # 项目行
        tree_data.append({
            "项目结构": f"📁 {project}",
            "项目名称": project,
            "标段名称": "",
            "项目总投资": "",
            "type": "project"
        })
        
        # 标段行
        if project in details:
            for detail in details[project]["rows"]:
                tree_data.append({
                    "项目结构": f"　　📄 {detail['name']}",
                    "项目名称": "",
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "type": "detail"
                })
    return tree_data

def convert_to_tree_data_v2(data, details):
    """方案2: 使用符号和层级标识"""
    tree_data = []
    for project in data:
        # 项目行
        tree_data.append({
            "层级显示": f"▼ {project}",
            "项目名称": project,
            "标段名称": "",
            "项目总投资": "",
            "type": "project"
        })
        
        # 标段行
        if project in details:
            for detail in details[project]["rows"]:
                tree_data.append({
                    "层级显示": f"　├─ {detail['name']}",
                    "项目名称": "",
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "type": "detail"
                })
    return tree_data

def convert_to_tree_data_v3(data, details):
    """方案3: 简单缩进"""
    tree_data = []
    for project in data:
        # 项目行
        tree_data.append({
            "显示名称": project,
            "项目名称": project,
            "标段名称": "",
            "项目总投资": "",
            "type": "project",
            "style_class": "project-row"
        })
        
        # 标段行
        if project in details:
            for detail in details[project]["rows"]:
                tree_data.append({
                    "显示名称": f"　　{detail['name']}",
                    "项目名称": "",
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "type": "detail",
                    "style_class": "detail-row"
                })
    return tree_data

@ui.page("/")
def home():
    """方案1: 图标版本"""
    ui.label("方案1: 图标树形表格").classes("text-xl font-bold mb-4")
    
    tree_data = convert_to_tree_data_v1(data, details)
    summary_data = rxui.use_pagination(tree_data, page_size=100)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=10")

        ui.aggrid({
            "columnDefs": [
                {
                    "headerName": "项目结构",
                    "field": "项目结构",
                    "width": 400,
                    "pinned": "left"
                },
                {
                    "headerName": "标段名称",
                    "field": "标段名称",
                    "width": 200
                },
                {
                    "headerName": "项目总投资",
                    "field": "项目总投资",
                    "width": 200
                }
            ],
            "rowData": summary_data.current_source.value,
            "defaultColDef": {
                "resizable": True,
                "sortable": True,
                "filter": True
            },
            "rowHeight": 40,
            "suppressRowHoverHighlight": False,
            "rowSelection": "single"
        }).classes("w-full h-[500px] border-2 border-gray-300")

@ui.page("/v2")
def home_v2():
    """方案2: 符号版本"""
    ui.label("方案2: 符号树形表格").classes("text-xl font-bold mb-4")
    
    tree_data = convert_to_tree_data_v2(data, details)
    summary_data = rxui.use_pagination(tree_data, page_size=100)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=10")

        ui.aggrid({
            "columnDefs": [
                {
                    "headerName": "层级结构",
                    "field": "层级显示",
                    "width": 400,
                    "pinned": "left"
                },
                {
                    "headerName": "标段名称",
                    "field": "标段名称",
                    "width": 200
                },
                {
                    "headerName": "项目总投资",
                    "field": "项目总投资",
                    "width": 200
                }
            ],
            "rowData": summary_data.current_source.value,
            "defaultColDef": {
                "resizable": True,
                "sortable": True,
                "filter": True
            },
            "rowHeight": 40,
            "suppressRowHoverHighlight": False,
            "rowSelection": "single"
        }).classes("w-full h-[500px] border-2 border-gray-300")

@ui.page("/v3")
def home_v3():
    """方案3: 简单版本"""
    ui.label("方案3: 简单树形表格").classes("text-xl font-bold mb-4")
    
    tree_data = convert_to_tree_data_v3(data, details)
    summary_data = rxui.use_pagination(tree_data, page_size=100)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=10")

        ui.aggrid({
            "columnDefs": [
                {
                    "headerName": "项目名称",
                    "field": "显示名称",
                    "width": 400
                },
                {
                    "headerName": "标段名称",
                    "field": "标段名称",
                    "width": 200
                },
                {
                    "headerName": "项目总投资",
                    "field": "项目总投资",
                    "width": 200
                }
            ],
            "rowData": summary_data.current_source.value,
            "defaultColDef": {
                "resizable": True,
                "sortable": True,
                "filter": True
            },
            "rowHeight": 40,
            "suppressRowHoverHighlight": False,
            "rowSelection": "single"
        }).classes("w-full h-[500px] border-2 border-gray-300")

@ui.page("/table")
def table_version():
    """备选: 使用NiceGUI原生table"""
    ui.label("备选方案: 原生表格").classes("text-xl font-bold mb-4")
    
    # 转换为表格数据
    rows = []
    for project in data[:10]:  # 限制显示数量
        rows.append({
            "项目名称": project,
            "标段名称": "",
            "项目总投资": ""
        })
        
        if project in details:
            for detail in details[project]["rows"]:
                rows.append({
                    "项目名称": f"　　{detail['name']}",
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"]
                })
    
    columns = [
        {"name": "项目名称", "label": "项目名称", "field": "项目名称", "align": "left"},
        {"name": "标段名称", "label": "标段名称", "field": "标段名称", "align": "left"},
        {"name": "项目总投资", "label": "项目总投资", "field": "项目总投资", "align": "right"}
    ]
    
    ui.table(columns=columns, rows=rows).classes("w-full")

if __name__ == "__main__":
    # 添加导航
    with ui.header():
        ui.label("树形表格方案对比").classes("text-white text-lg font-bold")
        with ui.row():
            ui.link("图标版", "/").classes("text-white")
            ui.link("符号版", "/v2").classes("text-white ml-4")
            ui.link("简单版", "/v3").classes("text-white ml-4")
            ui.link("原生表格", "/table").classes("text-white ml-4")
    
    ui.run()
