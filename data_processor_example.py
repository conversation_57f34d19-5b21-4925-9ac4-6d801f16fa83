#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理器使用示例
演示如何使用ProjectDataProcessor类处理项目数据
"""

from data_processor import ProjectDataProcessor, df_to_custom_dict, cal_result
import pandas as pd


def example_basic_usage():
    """基础使用示例"""
    print("=== 基础使用示例 ===")
    
    # 文件路径
    file_path = '/Users/<USER>/Downloads/统计表-2023、2024、2025年A、B类0730.xlsx'
    
    # 创建处理器
    processor = ProjectDataProcessor(file_path)
    
    # 加载和处理数据
    processor.load_data()
    processor.process_data()
    
    # 获取处理结果
    print("\n处理结果:")
    for year in [2023, 2024, 2025]:
        if year in processor.panel_rows:
            print(f"{year}年: {len(processor.panel_rows[year])} 个项目组")
    
    return processor


def example_data_access():
    """数据访问示例"""
    print("\n=== 数据访问示例 ===")
    
    processor = example_basic_usage()
    
    # 访问2025年的数据
    year = 2025
    if year in processor.data:
        df = processor.data[year]
        print(f"\n{year}年原始数据:")
        print(f"  数据形状: {df.shape}")
        print(f"  列名: {list(df.columns)[:5]}...")  # 显示前5个列名
        
        # 访问处理后的结果
        results = processor.results[year]
        details = processor.details[year]
        panel_rows = processor.panel_rows[year]
        
        print(f"\n{year}年处理后数据:")
        print(f"  结果数量: {len(results)}")
        print(f"  详情数量: {len(details)}")
        print(f"  面板行数: {len(panel_rows)}")
        
        # 显示第一个项目组的信息
        if panel_rows:
            first_key = panel_rows[0]
            print(f"\n第一个项目组信息:")
            print(f"  键: {first_key}")
            if first_key in details:
                detail_count = len(details[first_key])
                print(f"  详情记录数: {detail_count}")


def example_custom_processing():
    """自定义处理示例"""
    print("\n=== 自定义处理示例 ===")
    
    # 直接使用函数进行处理
    file_path = '/Users/<USER>/Downloads/统计表-2023、2024、2025年A、B类0730.xlsx'
    
    # 读取单个sheet
    df = pd.read_excel(file_path, sheet_name="2025年B类新建", skiprows=[0, 1, 2])
    df = df.drop(columns="序号")
    
    print(f"读取数据: {df.shape}")
    
    # 自定义分组列
    custom_ffill_col = ["项目名称", "项目编码", "建设单位"]
    
    # 前向填充
    df[custom_ffill_col] = df[custom_ffill_col].ffill()
    
    # 使用函数处理
    results = cal_result(df, custom_ffill_col)
    details = df_to_custom_dict(df, custom_ffill_col)
    
    print(f"自定义处理结果:")
    print(f"  分组数量: {len(results)}")
    print(f"  详情数量: {len(details)}")


def example_data_analysis():
    """数据分析示例"""
    print("\n=== 数据分析示例 ===")
    
    processor = ProjectDataProcessor('/Users/<USER>/Downloads/统计表-2023、2024、2025年A、B类0730.xlsx')
    processor.load_data()
    processor.process_data()
    
    # 分析各年度数据
    for year in [2023, 2024, 2025]:
        if year in processor.data:
            df = processor.data[year]
            
            print(f"\n{year}年数据分析:")
            
            # 基本统计
            print(f"  总项目数: {len(processor.panel_rows[year])}")
            
            # 按建设单位统计
            if '建设单位' in df.columns:
                unit_counts = df['建设单位'].value_counts()
                print(f"  建设单位数量: {len(unit_counts)}")
                print(f"  最多项目的单位: {unit_counts.index[0]} ({unit_counts.iloc[0]}个项目)")
            
            # 按项目性质统计
            if '项目性质' in df.columns:
                nature_counts = df['项目性质'].value_counts()
                print(f"  项目性质类型: {len(nature_counts)}")
                print(f"  主要项目性质: {nature_counts.index[0]} ({nature_counts.iloc[0]}个)")


def main():
    """主函数"""
    try:
        # 运行各种示例
        example_basic_usage()
        example_data_access()
        example_custom_processing()
        example_data_analysis()
        
        print("\n=== 所有示例运行完成 ===")
        
    except FileNotFoundError:
        print("错误: 找不到指定的Excel文件，请检查文件路径")
    except Exception as e:
        print(f"运行示例时出现错误: {e}")


if __name__ == "__main__":
    main()
