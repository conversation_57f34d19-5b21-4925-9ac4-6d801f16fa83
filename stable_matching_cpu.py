import os
import warnings
import time
import re
import pandas as pd
import numpy as np
from collections import defaultdict
from functools import lru_cache
from numba import njit
from concurrent.futures import ThreadPoolExecutor
import multiprocessing as mp
from typing import List, Tuple, Dict
import gc

warnings.filterwarnings('ignore')

class StableFileMatching:
    def __init__(self):
        self.start_time = time.time()
        
        # 文件路径配置
        self.p1 = '/Users/<USER>/Desktop/2025年项目资料/信息化项目里程碑及交付物标准 20240801_v4.xlsx'
        self.fileDF = '/Users/<USER>/Desktop/2025年项目资料/file0616.xlsx'
        self.profile = '/Users/<USER>/Documents/近三年投资计划/2018年至今项目清单（信息化项目）.xlsx'
        self.htp = '/Users/<USER>/Documents/科室项目管理周报/合同导数数据/合同查询导出_20250610143721870.xlsx'
        
        # 预编译正则表达式
        self.punctuation_pattern = re.compile(r'[，。、；：""''（）《》【】—…·～！？　 \n\r\t()\[\]{}!@#$%^&*\-_=+|\\:;"\'<>,.?0-9`~·！￥…（）《》【】——]+')
        
    def log_time(self, step_name: str):
        """记录执行时间"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        print(f"{step_name}: {elapsed:.2f} 秒")
        self.start_time = current_time

    @lru_cache(maxsize=2000)
    def clean_text(self, text: str) -> str:
        """缓存版本的文本清理"""
        if not text or pd.isna(text):
            return ""
        return self.punctuation_pattern.sub('', str(text))

    def safe_read_excel(self, file_path: str, **kwargs):
        """安全读取Excel文件"""
        try:
            return pd.read_excel(file_path, engine='calamine', **kwargs)
        except Exception as e:
            print(f"读取文件 {file_path} 失败: {e}")
            return pd.DataFrame()

    def load_all_data(self):
        """加载所有数据"""
        print("开始加载数据...")
        
        # 1. 加载标准数据
        type2biaozhun = {}
        try:
            s1 = self.safe_read_excel(self.p1, sheet_name='1_信息系统建设与升级改造', skiprows=[0])
            if not s1.empty and '交付物名称' in s1.columns:
                type2biaozhun['信息化'] = s1['交付物名称'].dropna().astype(str).tolist()
            
            s2 = self.safe_read_excel(self.p1, sheet_name='4_运维维护', skiprows=[0])
            if not s2.empty and '交付物名称' in s2.columns:
                s2_list = s2['交付物名称'].dropna().astype(str).tolist()
                type2biaozhun['信息维护修理'] = s2_list
                type2biaozhun['数据资产维护'] = s2_list
            
            s5 = self.safe_read_excel(self.p1, sheet_name='5_信息专题研究项目', skiprows=[0])
            if not s5.empty and '交付物名称' in s5.columns:
                type2biaozhun['专题研究'] = s5['交付物名称'].dropna().astype(str).tolist()
                
        except Exception as e:
            print(f"加载标准数据失败: {e}")
        
        self.log_time("标准数据加载")
        
        # 2. 加载项目数据
        ptypeall = pd.DataFrame()
        pronameall = []
        
        try:
            # 历年投资计划
            yearss = ['2022年年初', '2022年年中', '2023年年初', '2023年年中', '2024年年初', '2024年年中', '2025年年初']
            ptype1 = self.safe_read_excel(self.profile, sheet_name='历年投资计划')
            
            if not ptype1.empty and '投资年份' in ptype1.columns:
                ptype1 = ptype1[ptype1['投资年份'].isin(yearss)]
                required_cols = ['项目名称', '项目编码', '类型', '分部', '项目负责人']
                available_cols = [col for col in required_cols if col in ptype1.columns]
                ptype1 = ptype1[available_cols]
            
            # 历年信息维护修理
            ptype2 = self.safe_read_excel(self.profile, sheet_name='历年信息维护修理')
            if not ptype2.empty:
                rename_map = {'合同名称': '项目名称', '合同编号': '项目编码'}
                ptype2 = ptype2.rename(columns=rename_map)
                required_cols = ['项目名称', '项目编码', '类型', '分部', '项目负责人']
                available_cols = [col for col in required_cols if col in ptype2.columns]
                ptype2 = ptype2[available_cols].dropna(subset=['项目名称'])
            
            # 合并项目数据
            if not ptype1.empty and not ptype2.empty:
                ptypeall = pd.concat([ptype1, ptype2], axis=0, ignore_index=True)
            elif not ptype1.empty:
                ptypeall = ptype1
            elif not ptype2.empty:
                ptypeall = ptype2
            
            # 确保所有列都是字符串类型
            for col in ptypeall.columns:
                ptypeall[col] = ptypeall[col].astype(str)
            
            # 提取项目名称列表
            if '项目名称' in ptypeall.columns:
                pronameall = ptypeall['项目名称'].dropna().astype(str).tolist()
                pronameall = [name for name in pronameall if name and name != 'nan']
            
        except Exception as e:
            print(f"加载项目数据失败: {e}")
        
        self.log_time("项目数据加载")
        
        # 3. 加载合同数据
        htdf, htdf2 = [], []
        try:
            htp_data = self.safe_read_excel(self.htp)
            if not htp_data.empty and '合同编号' in htp_data.columns:
                htdf = htp_data['合同编号'].dropna().astype(str).tolist()
            
            if not ptype2.empty and '项目编码' in ptype2.columns:
                htdf2 = ptype2['项目编码'].dropna().astype(str).tolist()
        except Exception as e:
            print(f"加载合同数据失败: {e}")
        
        # 4. 加载主文件数据
        data00 = self.safe_read_excel(self.fileDF, sheet_name='2025-06-13')
        if data00.empty:
            print("主文件数据为空")
        
        self.log_time("主文件数据加载")
        
        return type2biaozhun, ptypeall, pronameall, htdf, htdf2, data00

    @njit
    def fast_lcs_length(self, a, b):
        """快速LCS长度计算"""
        m, n = len(a), len(b)
        if m == 0 or n == 0:
            return 0
        
        prev = np.zeros(n + 1, dtype=np.int32)
        
        for i in range(m):
            curr = np.zeros(n + 1, dtype=np.int32)
            for j in range(n):
                if a[i] == b[j]:
                    curr[j + 1] = prev[j] + 1
                else:
                    curr[j + 1] = max(prev[j + 1], curr[j])
            prev = curr
        
        return prev[n]

    def calculate_match_score(self, text1: str, text2: str) -> int:
        """计算匹配分数"""
        if not text1 or not text2:
            return 0
        
        try:
            a_bytes = np.frombuffer(text1.encode('utf-8'), dtype=np.uint8)
            b_bytes = np.frombuffer(text2.encode('utf-8'), dtype=np.uint8)
            return self.fast_lcs_length(a_bytes, b_bytes)
        except:
            return 0

    def process_single_file(self, args):
        """处理单个文件的匹配"""
        file_info, standards_cleaned, standards_raw = args
        project_name, file_name = file_info
        
        if not file_name or pd.isna(file_name):
            return None
        
        # 文本分割
        split_marks = ["-", " ", "_", "、", "：", "项目", "电网", "（", "）", "(", ")", "盖章"]
        text_parts = [str(file_name)]
        
        for mark in split_marks:
            new_parts = []
            for part in text_parts:
                if mark in part:
                    new_parts.extend(part.split(mark))
                else:
                    new_parts.append(part)
            text_parts = new_parts
        
        best_score = 0
        best_standard_idx = -1
        
        for part in text_parts:
            part_clean = self.clean_text(part)
            if len(part_clean) < 2:
                continue
            
            for idx, standard_clean in enumerate(standards_cleaned):
                if len(standard_clean) < 2:
                    continue
                
                score = self.calculate_match_score(standard_clean, part_clean)
                if score > best_score:
                    best_score = score
                    best_standard_idx = idx
        
        if best_score > 1:
            return (standards_raw[best_standard_idx], (str(file_name), str(project_name)), best_score)
        
        return None

    def batch_match_files(self, standards: List[str], file_list: List[Tuple], max_workers: int = None):
        """批量匹配文件"""
        if not standards or not file_list:
            return {}, file_list
        
        if not max_workers:
            max_workers = min(mp.cpu_count(), 4)  # 保守设置
        
        # 预处理标准列表
        standards_cleaned = [self.clean_text(std) for std in standards]
        
        # 准备任务
        tasks = [(file_info, standards_cleaned, standards) for file_info in file_list]
        
        results = defaultdict(list)
        no_matches = []
        
        try:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                match_results = list(executor.map(self.process_single_file, tasks))
            
            for file_info, result in zip(file_list, match_results):
                if result:
                    standard, match_pair, score = result
                    results[standard].append(match_pair)
                else:
                    no_matches.append(file_info)
        except Exception as e:
            print(f"批量匹配时出错: {e}")
            no_matches = file_list
        
        return dict(results), no_matches

    def process_main_data(self, data00, ptypeall, pronameall, htdf, htdf2):
        """处理主数据"""
        print("开始处理主数据...")
        
        if data00.empty:
            return pd.DataFrame()
        
        # 创建副本并确保所有列都是字符串类型
        result_data = data00.copy()
        
        # 预处理文件路径
        if '文件路径' in result_data.columns:
            result_data['文件路径'] = result_data['文件路径'].astype(str).str.replace(r'^L:\\审计提资项目资料\\', '', regex=True)
            result_data['文件名'] = result_data['文件路径'].str.rsplit('\\', n=1).str.get(-1)
        
        # 初始化新列
        result_data['分部'] = ''
        result_data['合同名称'] = ''
        result_data['项目编码'] = ''
        result_data['项目名称'] = ''
        
        # 部门匹配
        dept_list = ['数字应用部', '数据运营部', '安全运行部', '平台运维部', '架构与创新部', '信息调度与服务部']
        for dept in dept_list:
            mask = result_data['文件路径'].str.contains(dept, na=False)
            result_data.loc[mask, '分部'] = dept
        
        # 合同匹配
        all_contracts = htdf + htdf2
        for contract in all_contracts:
            if contract and str(contract) != 'nan':
                mask = result_data['文件路径'].str.contains(re.escape(str(contract)), na=False)
                result_data.loc[mask, '合同名称'] = str(contract)
        
        # 项目名称匹配
        for project_name in pronameall:
            if project_name and str(project_name) != 'nan':
                mask = result_data['文件路径'].str.contains(re.escape(str(project_name)), na=False)
                empty_mask = result_data['项目名称'] == ''
                result_data.loc[mask & empty_mask, '项目名称'] = str(project_name)
        
        # 合并项目信息
        if not ptypeall.empty and '项目名称' in ptypeall.columns:
            try:
                result_data = pd.merge(result_data, ptypeall, on='项目名称', how='left', suffixes=('', '_proj'))
            except Exception as e:
                print(f"合并项目信息时出错: {e}")
                result_data['类型'] = ''
                result_data['项目负责人'] = ''
        else:
            result_data['类型'] = ''
            result_data['项目负责人'] = ''
        
        # 清理数据
        result_data = result_data.drop_duplicates('文件路径')
        
        # 处理文件大小
        if '文件大小' in result_data.columns:
            try:
                result_data['文件大小'] = pd.to_numeric(result_data['文件大小'], errors='coerce').fillna(0) / 10000
            except:
                result_data['文件大小'] = 0
        
        # 选择最终列
        final_cols = ['文件路径', '文件大小', '文件类型', '分部', '合同名称', '项目编码', '项目名称', '类型', '项目负责人', '文件名']
        available_cols = [col for col in final_cols if col in result_data.columns]
        result_data = result_data[available_cols].fillna('')
        
        self.log_time("主数据处理完成")
        return result_data

    def run_stable_matching(self):
        """运行稳定的匹配流程"""
        print("=== 启动稳定版文件匹配系统 ===")
        total_start = time.time()

        # 1. 加载所有数据
        type2biaozhun, ptypeall, pronameall, htdf, htdf2, data00 = self.load_all_data()

        if data00.empty:
            print("主数据为空，退出程序")
            return None

        # 2. 处理主数据
        processed_data = self.process_main_data(data00, ptypeall, pronameall, htdf, htdf2)

        if processed_data.empty:
            print("处理后数据为空")
            return None

        # 3. 执行匹配
        print("开始执行文件匹配...")
        all_matches = {}
        all_no_matches = []

        # 按类型分组处理
        for match_type, standards in type2biaozhun.items():
            if not standards:
                continue

            type_data = processed_data[processed_data['类型'] == match_type]
            if type_data.empty:
                continue

            print(f"处理类型 '{match_type}': {len(type_data)} 个文件")

            file_list = [(row['项目名称'], row['文件名']) for _, row in type_data.iterrows()]
            matches, no_matches = self.batch_match_files(standards, file_list)

            # 合并结果
            for standard, match_list in matches.items():
                if standard not in all_matches:
                    all_matches[standard] = []
                all_matches[standard].extend(match_list)

            all_no_matches.extend(no_matches)

        self.log_time("匹配执行完成")

        # 4. 生成匹配结果
        print("生成匹配结果...")
        match_records = []
        for standard, matches in all_matches.items():
            for filename, project in matches:
                match_records.append({
                    '交付物标准': standard,
                    '文件名': filename,
                    '项目名称': project
                })

        # 5. 保存结果到Excel
        self.save_results_to_excel(processed_data, match_records, all_no_matches, type2biaozhun)

        # 6. 输出统计信息
        total_time = time.time() - total_start
        print(f"\n=== 执行统计 ===")
        print(f"总耗时: {total_time:.2f} 秒")
        print(f"处理文件: {len(processed_data)} 个")
        print(f"匹配成功: {len(match_records)} 个")
        print(f"未匹配: {len(all_no_matches)} 个")

        success_rate = len(match_records) / (len(match_records) + len(all_no_matches)) * 100 if (len(match_records) + len(all_no_matches)) > 0 else 0
        print(f"匹配成功率: {success_rate:.1f}%")

        return processed_data, match_records

    def save_results_to_excel(self, processed_data, match_records, no_matches, type2biaozhun):
        """简化版保存结果到Excel"""
        print("正在保存结果到Excel...")

        try:
            # 1. 匹配结果DataFrame
            if match_records:
                match_df = pd.DataFrame(match_records)
            else:
                match_df = pd.DataFrame()

            # 2. 未匹配文件DataFrame
            if no_matches:
                nomatch_df = pd.DataFrame(no_matches, columns=['项目名称', '文件名'])
            else:
                nomatch_df = pd.DataFrame()

            # 3. 保存到Excel
            output_file = '匹配结果.xlsx'
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                # 保存匹配结果
                if not match_df.empty:
                    match_df.to_excel(writer, sheet_name='匹配结果', index=False)

                # 保存处理后数据
                processed_data.to_excel(writer, sheet_name='处理数据', index=False)

                # 保存未匹配文件
                if not nomatch_df.empty:
                    nomatch_df.to_excel(writer, sheet_name='未匹配', index=False)

            print(f"✅ 结果已保存到: {output_file}")

        except Exception as e:
            print(f"❌ 保存Excel失败: {e}")
            # 备选：保存为CSV
            try:
                if match_records:
                    pd.DataFrame(match_records).to_csv('匹配结果.csv', index=False, encoding='utf-8-sig')
                processed_data.to_csv('处理数据.csv', index=False, encoding='utf-8-sig')
                print("✅ 已保存为CSV格式")
            except Exception as csv_error:
                print(f"❌ CSV保存也失败: {csv_error}")


def main():
    """主函数"""
    print("🚀 启动稳定版文件匹配系统...")

    matcher = StableFileMatching()
    try:
        result = matcher.run_stable_matching()
        if result:
            print("✅ 匹配流程完成!")
        else:
            print("❌ 匹配流程失败")
        return result
    except Exception as e:
        print(f"❌ 执行错误: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    result = main()
    gc.collect()  # 清理内存
    print("程序执行完毕")


