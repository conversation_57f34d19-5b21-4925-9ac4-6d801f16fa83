from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable

# 添加错误处理的数据加载
try:
    pp1 = '/Users/<USER>/Downloads/统计表(1).xlsx'
    dd1 = pd.read_excel(pp1, sheet_name='2023年B类新建', skiprows=[0, 2, 3])
    dd1项目名称 = dd1[['项目名称', '项目编码']].dropna().values.tolist()
    ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]

    data = [i for i in ll1项目名称]

    details = {
        row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
        for row in data
    }

    print(f"✅ 数据加载成功: {len(data)} 个项目")

except Exception as e:
    print(f"❌ 数据加载失败: {e}")
    # 使用示例数据避免启动失败
    data = ["示例项目A     编码001", "示例项目B     编码002", "示例项目C     编码003"]
    details = {
        row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
        for row in data
    }
    print(f"使用示例数据: {len(data)} 个项目")


# 修复路由问题 - 确保主页面存在
@ui.page("/")
def home():
    """主页面 - 重定向到AgGrid版本"""
    ui.navigate.to("/aggrid")


@ui.page("/aggrid")
def aggrid_version():
    """AgGrid版本 - 带展开控制"""

    ui.label("AgGrid可展开版本").classes("text-xl font-bold mb-4")

    # 展开状态
    expanded_state = ui.state({project: False for project in data})

    def get_display_data():
        """根据展开状态生成显示数据"""
        display_data = []

        for project in data:
            is_expanded = expanded_state.value.get(project, False)
            icon = "📂" if is_expanded else "📁"

            # 项目行
            display_data.append({
                "id": f"project_{project}",
                "项目名称": f"{icon} {project}",
                "标段名称": "",
                "项目总投资": "",
                "type": "project",
                "project_key": project
            })

            # 标段行（只有展开时显示）
            if is_expanded and project in details:
                for detail in details[project]["rows"]:
                    display_data.append({
                        "id": f"detail_{project}_{detail['name']}",
                        "项目名称": f"　　📄 {detail['name']}",
                        "标段名称": detail["name"],
                        "项目总投资": detail["value"],
                        "type": "detail",
                        "project_key": project
                    })

        return display_data

    # 初始数据
    display_data = get_display_data()
    summary_data = rxui.use_pagination(display_data, page_size=100)

    # 控制按钮
    with ui.row().classes("mb-4"):
        ui.button("全部展开", on_click=lambda: toggle_all(True)).classes("bg-green-500 text-white")
        ui.button("全部折叠", on_click=lambda: toggle_all(False)).classes("bg-red-500 text-white")

    def toggle_all(expand):
        new_state = {project: expand for project in data}
        expanded_state.value = new_state
        refresh_display()

    def refresh_display():
        new_data = get_display_data()
        summary_data.set_source(new_data)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=10")

        def handle_cell_click(event):
            """处理单元格点击"""
            try:
                if event and 'data' in event:
                    row_data = event['data']
                    if row_data.get('type') == 'project':
                        project_key = row_data.get('project_key')
                        if project_key:
                            # 切换展开状态
                            current_state = expanded_state.value.copy()
                            current_state[project_key] = not current_state[project_key]
                            expanded_state.value = current_state

                            refresh_display()

                            status = "展开" if current_state[project_key] else "折叠"
                            ui.notify(f"{status}: {project_key}")
            except Exception as e:
                print(f"点击处理错误: {e}")
                ui.notify("点击处理失败", type='negative')

        ui.aggrid({
            "columnDefs": [
                {
                    "headerName": "项目名称",
                    "field": "项目名称",
                    "width": 400
                },
                {
                    "headerName": "标段名称",
                    "field": "标段名称",
                    "width": 200
                },
                {
                    "headerName": "项目总投资",
                    "field": "项目总投资",
                    "width": 200
                }
            ],
            "rowData": summary_data.current_source.value,
            "defaultColDef": {
                "resizable": True,
                "sortable": True,
                "filter": True
            },
            "rowHeight": 40,
            "suppressRowHoverHighlight": False,
            "rowSelection": "single",
            "onCellClicked": handle_cell_click
        }).classes("w-full h-[500px] border-2 border-gray-300")



if __name__ == "__main__":
    print("启动应用...")

    # 添加导航
    with ui.header():
        ui.label("项目管理系统").classes("text-white text-lg font-bold")
        with ui.row():
            ui.link("AgGrid版", "/aggrid").classes("text-white")

    try:
        ui.run(
            host='127.0.0.1',
            port=8080,
            title='项目管理系统'
        )
    except Exception as e:
        print(f"启动失败: {e}")
        # 基础启动
        ui.run()