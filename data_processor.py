#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目数据处理模块
用于处理Excel项目统计表数据，包含数据读取、清洗和结构化处理功能
"""

import pandas as pd
import warnings
from typing import Dict, List, Tuple, Any, Optional


def df_to_custom_dict(df: pd.DataFrame, key_cols: List[str], value_cols: Optional[List[str]] = None) -> Dict[Tuple, List[Dict]]:
    """
    将DataFrame转换为自定义字典结构
    
    Args:
        df: 输入的DataFrame
        key_cols: 用作键的列名列表
        value_cols: 用作值的列名列表，如果为None则使用除key_cols外的所有列
    
    Returns:
        字典，键为key_cols组成的元组，值为包含value_cols数据的字典列表
    """
    if value_cols is None:
        value_cols = [col for col in df.columns if col not in key_cols]

    result = {}
    for _, row in df.iterrows():
        key = tuple(str(row[col]) for col in key_cols)
        value = {col: row[col] for col in value_cols}
        result.setdefault(key, []).append(value)

    return result


def cal_result(df: pd.DataFrame, ffill_col: List[str]) -> List[Dict[str, Any]]:
    """
    计算分组结果
    
    Args:
        df: 输入的DataFrame
        ffill_col: 用于分组的列名列表
    
    Returns:
        包含分组键和记录的字典列表
    """
    gp_df = df.groupby(ffill_col, dropna=False)

    return [
        {"key": tuple(str(k) for k in key), "records": group.to_dict(orient="records")}
        for key, group in gp_df
    ]


class ProjectDataProcessor:
    """项目数据处理器"""
    
    def __init__(self, file_path: str):
        """
        初始化数据处理器
        
        Args:
            file_path: Excel文件路径
        """
        self.file_path = file_path
        self.setup_pandas_options()
        
        # 定义列配置
        self.ffill_col = [
            "项目名称", "项目编码", "建设单位", "二级单位", "建设性质", "管理类别", "项目性质",
            "开始年", "结束年", "项目总投资合计", "项目总投资资本性", "项目总投资费用性",
            "分部", "项目负责人", "年初投资计划合计", "年初投资计划资本性", "年初投资计划费用性",
            "年中调整计划合计", "年中调整计划资本性", "年中调整计划费用性",
            "累计完成投资合计", "累计完成投资资本性", "累计完成投资费用性",
            "年度投资完成值合计", "年度投资完成值资本性", "年度投资完成值费用性"
        ]
        
        self.desired_cols = [
            "标段编号", "标段名称", "标段金额合计", "标段金额资本性", "标段金额费用性",
            "合同名称", "合同编号", "合同金额", "中标厂家", "是否跨项目签合同",
            "本标段对应合同金额", "结算方式", "是否暂定价", "分部", "合同负责人",
            "已支付金额", "未支付金额", "主标段采购进度", "是否主标段", "备注"
        ]
        
        # 列宽配置
        self.column_frs = "200px 120px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px"
        
        # 数据存储
        self.data = {}
        self.results = {}
        self.details = {}
        self.panel_rows = {}
    
    @staticmethod
    def setup_pandas_options():
        """设置pandas显示选项"""
        warnings.filterwarnings("ignore")
        pd.set_option("expand_frame_repr", False)
        pd.set_option("display.max_columns", None)
        pd.set_option("display.max_rows", None)
        pd.set_option("display.precision", 2)
        pd.set_option("display.float_format", "{:,.2f}".format)
    
    def load_data(self) -> None:
        """加载Excel数据"""
        print("开始加载数据...")
        
        # 加载2025年数据（A类+B类）
        dd1_b = pd.read_excel(self.file_path, sheet_name="2025年B类新建", skiprows=[0, 1, 2])
        dd1_a = pd.read_excel(self.file_path, sheet_name='2025年A类新建', skiprows=[0, 1, 2])
        self.data[2025] = dd1_b._append(dd1_a).drop(columns="序号")
        
        # 加载2024年数据（A类+B类）
        dd2_b = pd.read_excel(self.file_path, sheet_name="2024年B类新建", skiprows=[0, 1, 2])
        dd2_a = pd.read_excel(self.file_path, sheet_name='2024年A类新建', skiprows=[0, 1, 2])
        self.data[2024] = dd2_b._append(dd2_a).drop(columns="序号")
        
        # 加载2023年数据（A类+B类）
        dd3_b = pd.read_excel(self.file_path, sheet_name="2023年B类新建", skiprows=[0, 1, 2])
        dd3_a = pd.read_excel(self.file_path, sheet_name='2023年A类新建', skiprows=[0, 1, 2])
        self.data[2023] = dd3_b._append(dd3_a).drop(columns="序号")
        
        print(f"数据加载完成:")
        for year, df in self.data.items():
            print(f"  {year}年: {len(df)} 行数据")
    
    def process_data(self) -> None:
        """处理数据"""
        print("开始处理数据...")
        
        for year, df in self.data.items():
            # 转换年份列为字符串
            df[['开始年', '结束年']] = df[['开始年', '结束年']].astype('str')
            
            # 前向填充指定列
            df[self.ffill_col] = df[self.ffill_col].ffill()
            
            # 计算结果
            self.results[year] = cal_result(df, self.ffill_col)
            self.details[year] = df_to_custom_dict(df, self.ffill_col)
            self.panel_rows[year] = [row["key"] for row in self.results[year]]
            
            print(f"  {year}年处理完成: {len(self.panel_rows[year])} 个项目组")
    
    def get_data_summary(self) -> Dict[str, Any]:
        """获取数据摘要"""
        summary = {}
        for year in [2023, 2024, 2025]:
            if year in self.data:
                df = self.data[year]
                summary[year] = {
                    "总行数": len(df),
                    "项目组数": len(self.panel_rows.get(year, [])),
                    "列数": len(df.columns),
                    "ffill列数": len(self.ffill_col),
                    "desired列数": len(self.desired_cols)
                }
        return summary
    
    def export_processed_data(self, output_path: str) -> None:
        """导出处理后的数据"""
        with pd.ExcelWriter(output_path, engine='openpyxl') as writer:
            for year, df in self.data.items():
                df.to_excel(writer, sheet_name=f'{year}年处理后数据', index=False)
        print(f"处理后数据已导出到: {output_path}")


def main():
    """主函数"""
    # 文件路径
    file_path = '/Users/<USER>/Downloads/统计表-2023、2024、2025年A、B类0730.xlsx'
    
    # 创建数据处理器
    processor = ProjectDataProcessor(file_path)
    
    try:
        # 加载和处理数据
        processor.load_data()
        processor.process_data()
        
        # 打印摘要
        print("\n数据处理摘要:")
        summary = processor.get_data_summary()
        for year, info in summary.items():
            print(f"{year}年:")
            for key, value in info.items():
                print(f"  {key}: {value}")
        
        # 打印ffill_col配置
        print(f"\nffill_col配置 ({len(processor.ffill_col)}个字段):")
        for i, col in enumerate(processor.ffill_col, 1):
            print(f"  {i:2d}. {col}")
        
        print("\n数据处理完成！")
        
        # 可选：导出处理后的数据
        # processor.export_processed_data('processed_data.xlsx')
        
        return processor
        
    except Exception as e:
        print(f"处理过程中出现错误: {e}")
        return None


if __name__ == "__main__":
    processor = main()
