from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable
import warnings


def df_to_custom_dict(df, key_cols, value_cols=None):
    if value_cols is None:
        value_cols = [col for col in df.columns if col not in key_cols]

    result = {}
    for _, row in df.iterrows():
        key = tuple(str(row[col]) for col in key_cols)
        value = {col: row[col] for col in value_cols}
        result.setdefault(key, []).append(value)

    return result


def cal_result(df: pd.DataFrame, ffill_col: list[str]):
    gp_df = df.groupby(
        ffill_col, dropna=False
    )  # 把项目名称+项目编码合并成一个字符串做聚合

    return [
        {"key": tuple(str(k) for k in key), "records": group.to_dict(orient="records")}
        for key, group in gp_df
    ]


warnings.filterwarnings("ignore")
pd.set_option("expand_frame_repr", False)
pd.set_option("display.max_columns", None)
pd.set_option("display.max_rows", None)
pd.set_option("display.precision", 2)
pd.set_option("display.float_format", "{:,.2f}".format)

pp1 = '/Users/<USER>/Downloads/统计表-2023、2024、2025年A、B类.xlsx'
dd1 = pd.read_excel(pp1, sheet_name="2025年B类新建", skiprows=[0, 1, 2]).drop(
    columns="序号"
)
dd1[['开始年','结束年']]=dd1[['开始年','结束年']].astype('str')
dd2 = pd.read_excel(pp1, sheet_name="2024年B类新建", skiprows=[0, 1, 2]).drop(
    columns="序号"
)
dd2[['开始年','结束年']]=dd2[['开始年','结束年']].astype('str')
dd3 = pd.read_excel(pp1, sheet_name="2023年B类新建", skiprows=[0, 1, 2]).drop(
    columns="序号"
)
dd3[['开始年','结束年']]=dd3[['开始年','结束年']].astype('str')
ffill_col = [
    "项目名称",
    "项目编码",
    "建设单位",
    "二级单位",
    "建设性质",
    "管理类别",
    "项目性质",
    "开始年",
    "结束年",
    "项目总投资合计",
    "项目总投资资本性",
    "项目总投资费用性",
    "分部",
    "项目负责人"
]
dd1[ffill_col] = dd1[ffill_col].ffill()
dd2[ffill_col] = dd2[ffill_col].ffill()
dd3[ffill_col] = dd3[ffill_col].ffill()
desired_cols = [
    "标段编号",
    "标段名称",
    "标段金额合计",
    "标段金额资本性",
    "标段金额费用性",
    "合同名称",
    "合同编号",
    "合同金额",
    "中标厂家",
    "是否跨项目签合同",
    "本标段对应合同金额",
    "结算方式",
    "是否暂定价",
    "分部",
    "合同负责人",
    "已支付金额",
    "未支付金额",
    "主标段采购进度",
    "是否主标段",
    "备注",
]
# ---2025---

result2025 = cal_result(dd1, ffill_col)
details2025 = df_to_custom_dict(dd1, ffill_col)

def cc2025():
    ddd2025=pd.DataFrame(list(details2025.items()))
    return ddd2025



# ---2024---

result2024 = cal_result(dd2, ffill_col)
details2024 = df_to_custom_dict(dd2, ffill_col)

# ---2023---
result2023 = cal_result(dd3, ffill_col)

details2023 = df_to_custom_dict(dd3, ffill_col)


# ---2023---
panel_rows_2025 = [row["key"] for row in result2025]
print(panel_rows_2025)
panel_rows_2024 = [row["key"] for row in result2024]
panel_rows_2023 = [row["key"] for row in result2023]

column_frs = "200px 120px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px 70px"


@ui.page("/")
def home():
    ui.add_css(r"""
    .q-item__label {
      white-space: pre;
    }
    """)

    def ui_table_panel(panel_rows: list, year: int, details: dict):
        # 创建多个筛选选项
        project_options = ["全部"] + list(set([row[0] for row in panel_rows]))  # 项目名称
        unit_options = ["全部"] + list(set([row[2] for row in panel_rows]))     # 建设单位
        nature_options = ["全部"] + list(set([row[6] for row in panel_rows]))   # 项目性质

        selected_project = rxui.use_state("全部")
        selected_unit = rxui.use_state("全部")
        selected_nature = rxui.use_state("全部")

        # 根据多个筛选条件过滤数据
        def get_filtered_rows():
            filtered = panel_rows

            if selected_project.value != "全部":
                filtered = [row for row in filtered if row[0] == selected_project.value]

            if selected_unit.value != "全部":
                filtered = [row for row in filtered if row[2] == selected_unit.value]

            if selected_nature.value != "全部":
                filtered = [row for row in filtered if row[6] == selected_nature.value]

            return filtered

        filtered_rows = rxui.use_computed(get_filtered_rows)
        summary_data = rxui.use_pagination(filtered_rows, page_size=100)
        year_str = str(year) + "年"

        with ui.tab_panel(year_str):
            # 添加多个筛选控件
            with ui.column().classes("mb-4 gap-2"):
                with ui.row().classes("items-center gap-4 flex-wrap"):
                    ui.label("筛选条件:").classes("text-sm font-medium")

                    with ui.row().classes("items-center gap-2"):
                        ui.label("项目名称:").classes("text-xs")
                        ui.select(
                            options=project_options,
                            value=selected_project.value,
                            on_change=lambda e: selected_project.set_value(e.value)
                        ).classes("min-w-[150px]")

                    with ui.row().classes("items-center gap-2"):
                        ui.label("建设单位:").classes("text-xs")
                        ui.select(
                            options=unit_options,
                            value=selected_unit.value,
                            on_change=lambda e: selected_unit.set_value(e.value)
                        ).classes("min-w-[150px]")

                    with ui.row().classes("items-center gap-2"):
                        ui.label("项目性质:").classes("text-xs")
                        ui.select(
                            options=nature_options,
                            value=selected_nature.value,
                            on_change=lambda e: selected_nature.set_value(e.value)
                        ).classes("min-w-[120px]")

                with ui.row().classes("items-center gap-4"):
                    ui.label(f"共 {len(filtered_rows.value)} 个项目").classes("text-sm text-gray-600")
                    ui.button("重置筛选", on_click=lambda: [
                        selected_project.set_value("全部"),
                        selected_unit.set_value("全部"),
                        selected_nature.set_value("全部")
                    ]).props("size=sm color=grey outline")

            @effect_refreshable.on(summary_data)
            def on_summary_data_changed1():
                summary_data.create_q_pagination().props("max-pages=10")

                with ui.column().classes("gap-0 w-full items-stretch"):
                    with ui.grid(columns=column_frs).classes(
                        "place-items-center px-[16px] gap-2"
                    ):
                        for col in ffill_col:
                            ui.label(col)
                    with ui.column().classes("gap-0 w-full items-stretch"):
                        for row in summary_data.current_source.value:
                            with ui.expansion().classes(
                                "border-b-2 border-gray-300"
                            ).classes("w-[2000px]").props("dense") as expan_item:
                                with expan_item.add_slot("header"):
                                    with ui.grid(columns=column_frs).classes(
                                        "gap-2 items-center"
                                    ):
                                        for j in row:
                                            ui.badge(j)

                                ui.table(
                                    rows=details[row],
                                    columns=[
                                        {"name": "name", "field": col, "label": col}
                                        for col in desired_cols
                                        if col in dd1.columns
                                    ],
                                )

    with ui.tabs() as tabs:
        ui.tab("2023年")
        ui.tab("2024年")
        ui.tab("2025年")

    with ui.tab_panels(tabs, value="2025年").classes("w-full"):
        ui_table_panel(panel_rows_2023, 2023, details2023)
        ui_table_panel(panel_rows_2024, 2024, details2024)
        ui_table_panel(panel_rows_2025, 2025, details2025)




ui.run()
