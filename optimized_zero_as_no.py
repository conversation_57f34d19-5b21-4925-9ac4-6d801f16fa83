import pandas as pd
from openai import OpenAI
import time
from tqdm import tqdm

# 读取数据
pp1 = '/Users/<USER>/Desktop/2025年项目资料/文件日期/合并后的文件.xlsx'
data1 = pd.read_excel(pp1, sheet_name='Sheet1', engine='calamine')
data11 = data1.dropna(how='all', subset=data1.columns[2:]).fillna(0)

print(f"原始data11: {data11.shape}")

# 直接创建ld22 - 删掉前2列
ld22 = data11.iloc[:, 2:].values.tolist()

print(f"ld22: {len(ld22)} 行 x {len(ld22[0]) if ld22 else 0} 列")

# OpenAI客户端
client = OpenAI(
    api_key="sk-e1bfa2386d234bdf9498f84804075cce",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

def is_date_and_convert(text):
    """日期识别"""
    try:
        completion = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {'role': 'system', 'content': '你是日期识别专家。严格按要求返回，不要解释。'},
                {'role': 'user', 'content': f'判断"{text}"是否为日期。是日期返回"YYYY年MM月DD日"格式，不是返回"否"。'}
            ],
            temperature=0, max_tokens=20, top_p=0.1
        )
        
        result = completion.choices[0].message.content.strip()
        result = result.replace('。', '').replace('，', '').replace(',', '').replace('.', '')
        
        if any(word in result for word in ['否', '不是', '非', '无法', '不能']):
            return "否"
        
        if '年' in result and ('月' in result or '日' in result):
            return result
            
        return result if len(result) <= 15 else "否"
        
    except Exception as e:
        return "处理失败"

# 处理ld22
print("处理ld22...")
results = []
api_calls = 0  # API调用次数
zero_count = 0  # 0值数量
total_count = 0  # 总数量

for row_idx, row in enumerate(tqdm(ld22, desc="处理数据")):
    for col_idx, value in enumerate(row):
        total_count += 1
        
        # 如果值为0或空值，直接判断为"否"，不调用API
        if value == 0 or value == '0' or pd.isna(value) or value == '':
            results.append([
                data11.iloc[row_idx, 0],  # 第1列标识
                data11.iloc[row_idx, 1],  # 第2列标识
                f"第{col_idx+3}列",       # 列位置
                str(value),               # 原始值
                "否"                      # 直接判断为否
            ])
            zero_count += 1
            continue
        
        # 处理非0值，调用API
        result = is_date_and_convert(str(value))
        results.append([
            data11.iloc[row_idx, 0],  # 第1列标识
            data11.iloc[row_idx, 1],  # 第2列标识
            f"第{col_idx+3}列",       # 列位置
            str(value),               # 原始值
            result                    # 识别结果
        ])
        
        api_calls += 1
        
        # 显示进度
        if api_calls % 50 == 0:
            print(f"已调用API {api_calls} 次，处理0值 {zero_count} 个")
        
        # API延迟
        time.sleep(0.05)

# 保存结果
if results:
    df = pd.DataFrame(results, columns=["标识1", "标识2", "列位置", "原始值", "识别结果"])
    df.to_excel("优化处理结果.xlsx", index=False, engine='openpyxl')
    
    # 统计信息
    total = len(results)
    dates = len(df[df['识别结果'].str.contains('年', na=False)])
    no_results = len(df[df['识别结果'] == '否'])
    failed = len(df[df['识别结果'] == '处理失败'])
    
    print(f"\n=== 处理完成 ===")
    print(f"总数据量: {total_count} 个")
    print(f"0值数量: {zero_count} 个 ({zero_count/total_count*100:.1f}%)")
    print(f"API调用: {api_calls} 次 (节省了 {zero_count} 次调用)")
    print(f"识别为日期: {dates} 个")
    print(f"判断为否: {no_results} 个 (包含 {zero_count} 个0值)")
    print(f"处理失败: {failed} 个")
    print(f"日期识别率: {dates/total*100:.1f}%")
    
    # 显示处理示例
    print(f"\n=== 处理示例 ===")
    for i, (_, row) in enumerate(df.head(10).iterrows()):
        status = "🔍API" if row['原始值'] not in ['0', '0.0', 'nan', ''] else "⚡直接"
        print(f"{i+1}. {status} {row['标识1']} | {row['列位置']}: {row['原始值']} -> {row['识别结果']}")
    
    # 显示0值处理示例
    zero_df = df[df['原始值'].isin(['0', '0.0'])]
    if not zero_df.empty:
        print(f"\n=== 0值处理示例（前5个） ===")
        for i, (_, row) in enumerate(zero_df.head(5).iterrows()):
            print(f"{i+1}. ⚡ {row['标识1']} | {row['列位置']}: {row['原始值']} -> {row['识别结果']}")
    
    # 保存仅日期结果
    date_df = df[df['识别结果'].str.contains('年', na=False)]
    if not date_df.empty:
        date_df.to_excel("优化仅日期结果.xlsx", index=False, engine='openpyxl')
        print(f"\n=== 识别到的日期（前5个） ===")
        for i, (_, row) in enumerate(date_df.head(5).iterrows()):
            print(f"{i+1}. 📅 {row['标识1']} | {row['列位置']}: {row['原始值']} -> {row['识别结果']}")
        print(f"\n日期结果已单独保存到: 优化仅日期结果.xlsx")
    
    # 保存分类结果
    print(f"\n=== 保存分类结果 ===")
    
    # 0值结果
    if not zero_df.empty:
        zero_df.to_excel("0值处理结果.xlsx", index=False, engine='openpyxl')
        print(f"0值结果: 0值处理结果.xlsx ({len(zero_df)} 条)")
    
    # 非0值结果
    non_zero_df = df[~df['原始值'].isin(['0', '0.0'])]
    if not non_zero_df.empty:
        non_zero_df.to_excel("非0值处理结果.xlsx", index=False, engine='openpyxl')
        print(f"非0值结果: 非0值处理结果.xlsx ({len(non_zero_df)} 条)")
    
    print(f"\n完整结果: 优化处理结果.xlsx")

print(f"\n=== 性能优化效果 ===")
print(f"节省API调用: {zero_count} 次")
print(f"节省时间: 约 {zero_count * 0.05:.1f} 秒")
print(f"API调用效率: {api_calls}/{total_count} = {api_calls/total_count*100:.1f}%")

print("\n程序执行完毕！")
