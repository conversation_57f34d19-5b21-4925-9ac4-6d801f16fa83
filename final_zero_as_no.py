import pandas as pd
from openai import OpenAI
import time
from tqdm import tqdm

# 读取数据
pp1 = '/Users/<USER>/Desktop/2025年项目资料/文件日期/合并后的文件.xlsx'
data1 = pd.read_excel(pp1, sheet_name='Sheet1', engine='calamine')
data11 = data1.dropna(how='all', subset=data1.columns[2:]).fillna(0)

# 直接创建ld22 - 删掉前2列
ld22 = data11.iloc[:, 2:].values.tolist()

print(f"数据规模: {len(ld22)} 行 x {len(ld22[0]) if ld22 else 0} 列")

# OpenAI客户端
client = OpenAI(
    api_key="sk-e1bfa2386d234bdf9498f84804075cce",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

def is_date_and_convert(text):
    """日期识别"""
    try:
        completion = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {'role': 'system', 'content': '你是日期识别专家。严格按要求返回，不要解释。'},
                {'role': 'user', 'content': f'判断"{text}"是否为日期。是日期返回"YYYY年MM月DD日"格式，不是返回"否"。'}
            ],
            temperature=0, max_tokens=20, top_p=0.1
        )

        result = completion.choices[0].message.content.strip().replace('。', '').replace('，', '')
        
        if any(word in result for word in ['否', '不是', '非', '无法', '不能']):
            return "否"
        
        if '年' in result and ('月' in result or '日' in result):
            return result
            
        return result if len(result) <= 15 else "否"
        
    except:
        return "处理失败"

# 处理数据
print("开始处理...")
results = []
api_count = 0
zero_count = 0

for row_idx, row in enumerate(`tqdm`(ld22, desc="处理")):
    for col_idx, value in enumerate(row):
        # 0值直接判断为"否"
        if value == 0 or value == '0' or pd.isna(value) or value == '':
            result = "否"
            zero_count += 1
        else:
            # 非0值调用API
            result = is_date_and_convert(str(value))
            api_count += 1
            time.sleep(0.05)  # API延迟
        
        results.append([
            data11.iloc[row_idx, 0],  # 标识1
            data11.iloc[row_idx, 1],  # 标识2
            f"第{col_idx+3}列",       # 列位置
            str(value),               # 原始值
            result                    # 识别结果
        ])

# 保存结果
df = pd.DataFrame(results, columns=["标识1", "标识2", "列位置", "原始值", "识别结果"])
df.to_excel("最终处理结果.xlsx", index=False, engine='openpyxl')

# 统计
dates = len(df[df['识别结果'].str.contains('年', na=False)])
total = len(results)

print(f"\n=== 完成 ===")
print(f"总数据: {total} 个")
print(f"0值处理: {zero_count} 个 (直接判否)")
print(f"API调用: {api_count} 个")
print(f"识别日期: {dates} 个")
print(f"节省API调用: {zero_count} 次")

# 保存仅日期结果
date_df = df[df['识别结果'].str.contains('年', na=False)]
if not date_df.empty:
    date_df.to_excel("仅日期结果.xlsx", index=False, engine='openpyxl')
    print(f"日期结果已保存")

print("完成！")
