# DeepSeek API 批量文本处理程序

这是一个使用OpenAI库调用DeepSeek API进行批量文本处理的Python程序。

## 功能特性

- 🔄 批量处理大量文本
- 📊 支持多种文本处理任务（总结、翻译、情感分析等）
- 💾 自动保存处理结果（JSON和CSV格式）
- 📈 提供详细的处理统计信息
- ⚡ 支持分批处理，避免API限制
- 🎯 支持交互式和命令行两种使用模式

## 安装依赖

1. 确保已激活虚拟环境：
```bash
source venv/bin/activate  # macOS/Linux
# 或
venv\Scripts\activate     # Windows
```

2. 安装依赖包：
```bash
pip install -r requirements.txt
```

## 配置API密钥

1. 创建 `.env` 文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，添加你的DeepSeek API密钥：
```
DEEPSEEK_API_KEY=your_deepseek_api_key_here
```

## 使用方法

### 交互式模式

直接运行程序，按提示操作：

```bash
python main.py
```

程序会引导你：
1. 选择数据来源（示例数据、文件、手动输入）
2. 选择处理类型（总结、翻译、情感分析等）
3. 设置输出文件名
4. 开始批量处理

### 命令行模式

```bash
python main.py <input_file> <output_file> <prompt> [model]
```

参数说明：
- `input_file`: 输入文本文件路径（每行一个文本）
- `output_file`: 输出结果文件路径
- `prompt`: 处理提示词
- `model`: 可选，使用的模型名称（默认：deepseek-chat）

示例：
```bash
# 总结文本
python main.py input.txt results.json "请对以下文本进行简洁的总结："

# 翻译文本
python main.py input.txt results.json "请将以下中文文本翻译成英文："

# 情感分析
python main.py input.txt results.json "请分析以下文本的情感倾向（正面、负面或中性）："
```

## 支持的处理类型

程序内置了以下处理类型：

1. **summarize**: 文本总结
2. **translate**: 中英翻译
3. **sentiment**: 情感分析
4. **keywords**: 关键词提取
5. **improve**: 文本改进
6. **classify**: 文本分类
7. **custom**: 自定义提示词

## 输出格式

程序会生成两个文件：

1. **JSON文件** (`results.json`): 包含完整的处理结果和元数据
2. **CSV文件** (`results.csv`): 便于在Excel等工具中查看

输出字段包括：
- `original_text`: 原始文本
- `processed_text`: 处理后的文本
- `model`: 使用的模型
- `usage`: API使用情况（token数等）
- `status`: 处理状态（success/error）
- `error`: 错误信息（如果有）

## 配置选项

可以在 `config.py` 中修改以下配置：

```python
# 模型配置
DEFAULT_MODEL = "deepseek-chat"
MAX_TOKENS = 4096
TEMPERATURE = 0.7

# 批量处理配置
BATCH_SIZE = 10  # 每批处理的文本数量
DELAY_BETWEEN_BATCHES = 1  # 批次间延迟（秒）
```

## 示例数据

程序包含15个示例文本，涵盖人工智能、机器学习、云计算等主题，可以直接用于测试。

## 注意事项

1. **API密钥安全**: 请妥善保管你的API密钥，不要提交到版本控制系统
2. **API限制**: 程序已内置延迟机制，避免触发API频率限制
3. **文件编码**: 确保输入文件使用UTF-8编码
4. **网络连接**: 需要稳定的网络连接访问DeepSeek API

## 错误处理

程序包含完善的错误处理机制：
- API调用失败时会记录错误信息
- 网络问题时会自动重试
- 处理统计会显示成功率和失败原因

## 许可证

MIT License 