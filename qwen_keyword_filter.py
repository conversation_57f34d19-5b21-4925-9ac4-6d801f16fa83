import pandas as pd
from openai import OpenAI
import os
import json
import time
from tqdm import tqdm

# 读取数据路径
p1 = '/Users/<USER>/Desktop/2025年项目资料/file0607.xlsx'
p2 = '/Users/<USER>/Documents/近三年投资计划/2018年至今项目清单（信息化项目）.xlsx'

# 读取文件名和项目名称列表
filenames = pd.read_excel(p1, sheet_name='2025-06-05', engine='calamine')['文件名'].astype('str').tolist()
pronames1 = pd.read_excel(p2, sheet_name='历年投资计划', engine='calamine')['项目名称'].astype('str').tolist()
pronames2 = pd.read_excel(p2, sheet_name='历年信息维护修理', engine='calamine')['合同名称'].astype('str').tolist()
pronames3 = pronames1 + pronames2

print(f"加载了 {len(filenames)} 个文件名")
print(f"加载了 {len(pronames3)} 个项目关键词")

# 设置批处理参数
batch_size = 10  # 进一步减少批次大小以获得更精确的结果
batches = [filenames[i:i + batch_size] for i in range(0, len(filenames), batch_size)]

client = OpenAI(
    api_key="sk-e1bfa2386d234bdf9498f84804075cce",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

def process_filename_batch(file_batch, keywords):
    """处理一批文件名"""
    # 限制关键词数量以避免token过多
    limited_keywords = keywords[:150]
    keywords_text = "\n".join([f"- {kw}" for kw in limited_keywords])
    
    files_text = "\n".join([f"{i+1}. {filename}" for i, filename in enumerate(file_batch)])
    
    prompt = f"""请根据项目关键词列表，对每个文件名进行关键词过滤处理。

项目关键词列表：
{keywords_text}

待处理的文件名：
{files_text}

处理要求：
1. 分析每个文件名，识别其中包含的项目关键词（支持模糊匹配、部分匹配、同义词匹配）
2. 从文件名中删除匹配的关键词部分
3. 保留文件名中有意义的剩余部分
4. 保持文件扩展名不变
5. 如果没有匹配的关键词，保持原文件名

请按以下格式返回结果，每行一个文件名的处理结果：
原文件名1 -> 过滤后文件名1
原文件名2 -> 过滤后文件名2
...

只返回处理结果，不要添加其他说明文字。"""
    
    try:
        response = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {"role": "system", "content": "你是一个专业的文件名处理专家，擅长根据关键词过滤文件名。"},
                {"role": "user", "content": prompt},
            ],
            temperature=0.1,
            max_tokens=3000,
            stream=False
        )
        
        return response.choices[0].message.content.strip()
    
    except Exception as e:
        print(f"API调用失败: {str(e)}")
        return None

def parse_response(response_text, original_batch):
    """解析AI响应"""
    results = []
    
    if not response_text:
        # 如果响应为空，返回原文件名
        for filename in original_batch:
            results.append({
                "原始文件名": filename,
                "过滤后文件名": filename,
                "是否有匹配": False,
                "处理状态": "API调用失败"
            })
        return results
    
    lines = response_text.strip().split('\n')
    processed_files = {}
    
    # 解析 "原文件名 -> 过滤后文件名" 格式
    for line in lines:
        if '->' in line:
            parts = line.split('->')
            if len(parts) == 2:
                original = parts[0].strip()
                filtered = parts[1].strip()
                
                # 移除可能的序号
                original = original.split('. ', 1)[-1] if '. ' in original else original
                
                processed_files[original] = filtered
    
    # 为每个原始文件名创建结果
    for filename in original_batch:
        filtered_name = processed_files.get(filename, filename)
        
        # 判断是否有匹配（文件名是否发生了变化）
        has_match = filtered_name != filename
        
        results.append({
            "原始文件名": filename,
            "过滤后文件名": filtered_name,
            "是否有匹配": has_match,
            "处理状态": "成功"
        })
    
    return results

# 处理所有批次
all_results = []
total_batches = len(batches)

print(f"开始处理 {total_batches} 个批次...")

for batch_idx, batch in enumerate(tqdm(batches, desc="处理文件名")):
    print(f"\n处理第 {batch_idx + 1}/{total_batches} 批次")
    
    # 处理当前批次
    response_text = process_filename_batch(batch, pronames3)
    
    # 解析结果
    batch_results = parse_response(response_text, batch)
    
    # 添加批次信息
    for result in batch_results:
        result["批次编号"] = batch_idx + 1
        all_results.append(result)
    
    # 显示本批次的处理结果
    print("本批次处理结果：")
    for result in batch_results:
        if result["是否有匹配"]:
            print(f"  ✓ {result['原始文件名']} -> {result['过滤后文件名']}")
        else:
            print(f"  - {result['原始文件名']} (无变化)")
    
    # 添加延迟
    if batch_idx < total_batches - 1:
        time.sleep(0.5)

# 创建结果DataFrame
results_df = pd.DataFrame(all_results)

# 保存详细结果
detailed_output = "qwen_keyword_filter_detailed.xlsx"
results_df.to_excel(detailed_output, index=False, engine='openpyxl')

# 创建简化版本 - 只包含过滤后的文件名
filtered_filenames = [result["过滤后文件名"] for result in all_results]
simple_df = pd.DataFrame({"过滤后的文件名": filtered_filenames})
simple_output = "qwen_filtered_filenames_only.xlsx"
simple_df.to_excel(simple_output, index=False, engine='openpyxl')

# 创建对比版本 - 原文件名和过滤后文件名对比
comparison_df = pd.DataFrame({
    "原始文件名": [result["原始文件名"] for result in all_results],
    "过滤后文件名": [result["过滤后文件名"] for result in all_results],
    "是否有变化": [result["是否有匹配"] for result in all_results]
})
comparison_output = "qwen_filename_comparison.xlsx"
comparison_df.to_excel(comparison_output, index=False, engine='openpyxl')

# 输出统计信息
print(f"\n=== 处理完成 ===")
print(f"总文件数: {len(all_results)}")
success_count = len([r for r in all_results if r["处理状态"] == "成功"])
match_count = len([r for r in all_results if r["是否有匹配"]])
print(f"成功处理: {success_count}")
print(f"有关键词匹配的文件: {match_count}")
print(f"匹配率: {match_count/len(all_results)*100:.1f}%")

print(f"\n=== 输出文件 ===")
print(f"详细结果: {detailed_output}")
print(f"仅过滤后文件名: {simple_output}")
print(f"对比结果: {comparison_output}")

# 显示一些有变化的示例
print(f"\n=== 处理示例（有变化的文件名） ===")
changed_results = [r for r in all_results if r["是否有匹配"]][:10]
for i, result in enumerate(changed_results, 1):
    print(f"{i}. 原文件名: {result['原始文件名']}")
    print(f"   过滤后: {result['过滤后文件名']}")
    print()

if not changed_results:
    print("没有文件名发生变化，可能需要调整关键词匹配策略")

print("处理完成！")
