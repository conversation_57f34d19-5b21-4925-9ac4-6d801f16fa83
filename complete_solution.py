from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable

# 您的原始数据
pp1='/Users/<USER>/Downloads/统计表(1).xlsx'
dd1=pd.read_excel(pp1,sheet_name='2023年B类新建',skiprows=[0,2,3])
dd1项目名称 = dd1[['项目名称', '项目编码']].dropna().values.tolist()
ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]

data = [i for i in ll1项目名称]
details = {
    row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
    for row in data
}

@ui.page("/")
def home():
    """解决JSON序列化问题的版本"""
    
    # 数据转换函数
    def convert_to_tree_display():
        """转换数据为带缩进的显示格式"""
        display_data = []
        
        for project in data:
            # 项目行 - 加粗显示
            display_data.append({
                "显示名称": f"📁 {project}",
                "项目名称": project,
                "标段名称": "",
                "项目总投资": "",
                "类型": "项目",
                "层级": 0
            })
            
            # 标段行 - 缩进显示
            if project in details:
                for detail in details[project]["rows"]:
                    display_data.append({
                        "显示名称": f"　　📄 {detail['name']}",
                        "项目名称": "",
                        "标段名称": detail["name"],
                        "项目总投资": detail["value"],
                        "类型": "标段",
                        "层级": 1
                    })
        
        return display_data
    
    # 转换数据
    tree_data = convert_to_tree_display()
    summary_data = rxui.use_pagination(tree_data, page_size=100)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=10")

        # 完全避免函数的AgGrid配置
        grid_config = {
            "columnDefs": [
                {
                    "headerName": "项目结构",
                    "field": "显示名称",
                    "width": 400,
                    "pinned": "left"
                },
                {
                    "headerName": "标段名称",
                    "field": "标段名称", 
                    "width": 200
                },
                {
                    "headerName": "项目总投资",
                    "field": "项目总投资",
                    "width": 150
                }
            ],
            "rowData": summary_data.current_source.value,
            "defaultColDef": {
                "resizable": True,
                "sortable": True,
                "filter": True,
                "wrapText": True,
                "autoHeight": False
            },
            "rowHeight": 40,
            "headerHeight": 35,
            "animateRows": True,
            "rowSelection": "single",
            "suppressRowClickSelection": False,
            "suppressCellSelection": True
        }
        
        ui.aggrid(grid_config).classes("w-full h-[600px] border-2 border-gray-300")

@ui.page("/alternative")
def alternative():
    """备选方案 - 使用NiceGUI原生组件"""
    
    ui.label("备选方案 - 原生组件").classes("text-xl font-bold mb-4")
    
    # 转换为表格数据
    table_rows = []
    for project in data[:10]:  # 限制显示数量
        # 项目行
        table_rows.append({
            "项目名称": project,
            "标段名称": "",
            "项目总投资": "",
            "样式": "font-weight: bold; background-color: #f5f5f5;"
        })
        
        # 标段行
        if project in details:
            for detail in details[project]["rows"]:
                table_rows.append({
                    "项目名称": "　　" + detail["name"],  # 手动缩进
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "样式": "padding-left: 20px;"
                })
    
    # 表格列定义
    columns = [
        {"name": "项目名称", "label": "项目名称", "field": "项目名称", "align": "left"},
        {"name": "标段名称", "label": "标段名称", "field": "标段名称", "align": "left"},
        {"name": "项目总投资", "label": "项目总投资", "field": "项目总投资", "align": "right"}
    ]
    
    # 创建表格
    ui.table(columns=columns, rows=table_rows).classes("w-full")

@ui.page("/debug")
def debug_page():
    """调试页面 - 检查数据"""
    
    ui.label("数据调试页面").classes("text-xl font-bold mb-4")
    
    # 显示原始数据信息
    with ui.column().classes("gap-4"):
        ui.label(f"data数量: {len(data)}")
        ui.label(f"details数量: {len(details)}")
        
        if data:
            ui.label("前3个项目:")
            for i, project in enumerate(data[:3]):
                ui.label(f"  {i+1}. {project}")
        
        # 测试JSON序列化
        ui.button("测试数据序列化", on_click=test_serialization)
        
        result_area = ui.textarea("测试结果").classes("w-full h-32")
        
        def test_serialization():
            import json
            try:
                # 测试简单数据
                test_data = [
                    {"项目名称": "测试项目", "标段名称": "测试标段", "项目总投资": "100"}
                ]
                json.dumps(test_data)
                result_area.value = "✅ 基础数据可以序列化"
                
                # 测试转换后的数据
                tree_data = []
                for i, project in enumerate(data[:2]):
                    tree_data.append({
                        "项目名称": project,
                        "标段名称": "",
                        "项目总投资": ""
                    })
                    if project in details:
                        for detail in details[project]["rows"]:
                            tree_data.append({
                                "项目名称": "",
                                "标段名称": detail["name"],
                                "项目总投资": detail["value"]
                            })
                
                json.dumps(tree_data)
                result_area.value += "\n✅ 转换后数据也可以序列化"
                result_area.value += f"\n转换后数据量: {len(tree_data)}"
                
            except Exception as e:
                result_area.value = f"❌ 序列化失败: {e}"

if __name__ == "__main__":
    # 添加导航
    with ui.header():
        ui.label("项目管理系统").classes("text-white text-lg font-bold")
        with ui.row():
            ui.link("主页", "/").classes("text-white")
            ui.link("备选方案", "/alternative").classes("text-white ml-4")
            ui.link("调试", "/debug").classes("text-white ml-4")
    
    print("启动应用...")
    ui.run()
