import pandas as pd

# 读取数据
dd1 = pd.read_excel(pp1, sheet_name='2025年B类新建', skiprows=[0,1,2])
print(dd1.head(5))

# 获取项目名称和编码
dd1项目名称 = dd1[['项目名称','项目编码']].dropna().values.tolist()
print(dd1项目名称)

ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]
data = [i for i in ll1项目名称]

# 指定需要的列
desired_cols = ["标段名称", "标段编号", "合同名称", "合同编号", "合同金额", "中标厂家", 
                '是否跨项目签合同', '本标段对应合同金额']

# 获取dd1中实际存在的列
available_cols = [col for col in desired_cols if col in dd1.columns]
print(f"可用的列: {available_cols}")

# 直接替换 - 只使用指定列的值
details = {}

for i, row_key in enumerate(data):
    # 为每行选择最多3个可用列
    rows_data = []
    
    # 从可用列中选择最多3个
    for j, col in enumerate(available_cols[:3]):
        value = dd1.iloc[i][col] if i < len(dd1) else ""
        rows_data.append({
            "name": col,
            "value": str(value) if pd.notna(value) else ""
        })
    
    # 如果可用列少于3个，用空值补齐
    while len(rows_data) < 3:
        rows_data.append({
            "name": f"字段{len(rows_data)+1}",
            "value": ""
        })
    
    details[row_key] = {"rows": rows_data}

# 显示结果
print("\n替换后的details:")
for key, value in list(details.items())[:3]:  # 显示前3个
    print(f"{key}: {value}")
