# Qwen3.0 文件名关键词处理工具

这个工具使用Qwen3.0 API来处理文件名中的关键词匹配和删除。根据A列表（关键词列表）的元素作为关键词，在B列表（文件名列表）中的每一个文件名中进行模糊查询，找到相关元素包含A列中的关键词就把关键词删掉，保留剩下的文件名。

## 功能特点

- 🔍 **智能模糊匹配**: 使用Qwen3.0进行智能的关键词匹配，支持完全匹配、部分匹配、同义词匹配
- 📝 **批量处理**: 支持大批量文件名的处理，自动分批避免API限制
- 💾 **多格式输出**: 支持JSON和CSV格式的结果输出
- 📊 **详细统计**: 提供处理成功率、匹配率等详细统计信息
- 🎯 **灵活输入**: 支持文件输入和手动输入两种方式

## 安装依赖

```bash
pip install -r requirements.txt
```

## 配置API密钥

1. 复制环境变量示例文件：
```bash
cp .env.example .env
```

2. 编辑 `.env` 文件，填入你的Qwen API密钥：
```
QWEN_API_KEY=your_qwen_api_key_here
QWEN_BASE_URL=https://dashscope.aliyuncs.com/compatible-mode/v1
```

## 使用方法

### 1. 交互式模式

直接运行主程序：
```bash
python filename_processor.py
```

程序会引导你：
1. 输入关键词列表（A列表）- 可以从文件读取或手动输入
2. 输入文件名列表（B列表）- 可以从文件读取或手动输入
3. 选择输出文件名
4. 开始处理

### 2. 命令行批处理模式

```bash
python filename_processor.py <关键词文件> <文件名文件> <输出文件> [处理后文件名保存路径]
```

示例：
```bash
python filename_processor.py sample_keywords.txt sample_filenames.txt results.json remaining_filenames.txt
```

### 3. 使用示例数据测试

项目包含了示例数据文件：
- `sample_keywords.txt`: 示例关键词列表
- `sample_filenames.txt`: 示例文件名列表

可以直接使用这些文件进行测试。

## 测试

运行测试脚本：
```bash
python test_filename_processor.py
```

或者运行特定测试：
```bash
python test_filename_processor.py single   # 单个文件名测试
python test_filename_processor.py batch    # 批量处理测试
python test_filename_processor.py sample   # 示例文件测试
```

## 输出格式

### CSV输出格式
| 原始文件名 | 是否匹配 | 匹配的关键词 | 处理后文件名 | 说明 | 状态 |
|------------|----------|--------------|--------------|------|------|
| 技术方案设计文档v1.0.docx | True | 技术, 文档 | 方案设计v1.0.docx | ... | success |

### JSON输出格式
```json
{
  "original_filename": "技术方案设计文档v1.0.docx",
  "keywords": ["技术", "报告", "文档"],
  "has_match": true,
  "matched_keywords": ["技术", "文档"],
  "remaining_filename": "方案设计v1.0.docx",
  "explanation": "匹配原因说明",
  "status": "success"
}
```

## 处理逻辑

1. **模糊匹配**: 使用Qwen3.0的自然语言理解能力进行智能匹配
2. **匹配类型**:
   - 完全匹配：关键词完全出现在文件名中
   - 部分匹配：关键词的一部分出现在文件名中
   - 同义词匹配：文件名中包含关键词的同义词
   - 相关概念匹配：文件名中包含与关键词相关的概念

3. **关键词删除**: 智能删除匹配的关键词，保持文件名的可读性

## 注意事项

1. **API配置**: 确保正确配置Qwen API密钥和基础URL
2. **文件编码**: 输入文件请使用UTF-8编码
3. **API限制**: 程序会自动处理API调用频率限制
4. **错误处理**: 程序包含完善的错误处理机制

## 文件结构

```
.
├── filename_processor.py          # 主程序
├── qwen_file_processor.py         # Qwen处理器核心类
├── test_filename_processor.py     # 测试脚本
├── config.py                      # 配置文件
├── sample_keywords.txt            # 示例关键词
├── sample_filenames.txt           # 示例文件名
├── .env.example                   # 环境变量示例
└── README_filename_processor.md   # 使用说明
```

## 示例

假设有以下数据：

**关键词列表 (A列表)**:
```
技术
项目
系统
```

**文件名列表 (B列表)**:
```
技术方案设计文档v1.0.docx
项目管理计划书.pdf
系统开发进度报告.xlsx
用户需求分析文档.docx
```

**处理结果**:
```
方案设计v1.0.docx
管理计划书.pdf
开发进度报告.xlsx
用户需求分析文档.docx
```

## 故障排除

1. **API密钥错误**: 检查 `.env` 文件中的API密钥是否正确
2. **网络连接问题**: 确保网络连接正常，可以访问Qwen API
3. **文件编码问题**: 确保输入文件使用UTF-8编码
4. **依赖包问题**: 运行 `pip install -r requirements.txt` 安装所有依赖
