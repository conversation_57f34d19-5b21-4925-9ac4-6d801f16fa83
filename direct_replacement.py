from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable

# 假设您已有的数据和details
def load_project_data():
    """加载项目数据"""
    try:
        pp1 = '/Users/<USER>/Downloads/统计表(1).xlsx'
        dd1 = pd.read_excel(pp1, sheet_name='2023年B类新建', skiprows=[0, 2, 3])
        dd1项目名称 = dd1[['项目名称', '项目编码']].dropna().values.tolist()
        ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]
        data = [i for i in ll1项目名称]
        return data
    except Exception as e:
        print(f"加载数据失败: {e}")
        return []

def create_project_details(data):
    """创建项目详情"""
    details = {
        row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
        for row in data
    }
    return details

# 加载数据
data = load_project_data()
details = create_project_details(data)

def prepare_aggrid_data(data, details):
    """准备AgGrid树形数据"""
    aggrid_rows = []
    
    for project in data:
        # 父节点 - 项目
        aggrid_rows.append({
            "项目名称": project,
            "标段名称": "",
            "项目总投资": "",
            "type": "project",
            "orgHierarchy": [project]  # 树形结构路径
        })
        
        # 子节点 - 标段详情
        if project in details:
            for detail in details[project]["rows"]:
                aggrid_rows.append({
                    "项目名称": "",
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "type": "detail",
                    "orgHierarchy": [project, detail["name"]]  # 树形结构路径
                })
    
    return aggrid_rows

@ui.page("/")
def home():
    """使用AgGrid树形表格替换原始expansion"""
    
    # 准备树形数据
    aggrid_data = prepare_aggrid_data(data, details)
    
    # 使用分页
    summary_data = rxui.use_pagination(aggrid_data, page_size=100)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        # 分页控件
        summary_data.create_q_pagination().props("max-pages=10")

        # AgGrid 树形表格配置
        grid_options = {
            "columnDefs": [
                {
                    "headerName": "项目名称",
                    "field": "项目名称",
                    "width": 400,
                    "cellRenderer": "agGroupCellRenderer",
                    "cellRendererParams": {
                        "innerRenderer": lambda params: params.get('value', ''),
                        "suppressCount": True
                    }
                },
                {
                    "headerName": "标段名称",
                    "field": "标段名称",
                    "width": 200
                },
                {
                    "headerName": "项目总投资",
                    "field": "项目总投资",
                    "width": 200
                }
            ],
            "rowData": summary_data.current_source.value,
            "treeData": True,
            "animateRows": True,
            "groupDefaultExpanded": 1,  # 默认展开第一层
            "getDataPath": lambda data: data.get("orgHierarchy", []),
            "autoGroupColumnDef": {
                "headerName": "项目结构",
                "width": 300,
                "cellRendererParams": {
                    "suppressCount": True,
                    "checkbox": False
                }
            },
            "defaultColDef": {
                "resizable": True,
                "sortable": True,
                "filter": True
            },
            "rowHeight": 35,
            "headerHeight": 40
        }
        
        # 创建AgGrid表格
        ui.aggrid(grid_options).classes("w-full h-[600px] border border-gray-300")

# 运行应用
ui.run()
