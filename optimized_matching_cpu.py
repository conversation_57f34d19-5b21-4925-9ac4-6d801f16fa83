import os
import warnings
import time
import re
import pandas as pd
import numpy as np
from collections import defaultdict
from functools import lru_cache
from numba import njit
from concurrent.futures import ThreadPoolExecutor, ProcessPoolExecutor
import multiprocessing as mp
from typing import List, Tuple, Dict, Any
import gc

warnings.filterwarnings('ignore')

# 优化pandas设置
pd.set_option('expand_frame_repr', False)
pd.set_option('display.max_columns', None)
pd.set_option('display.max_rows', None)
pd.set_option('display.precision', 2)
pd.set_option('display.float_format', '{:,.2f}'.format)

class OptimizedFileMatching:
    def __init__(self):
        self.start_time = time.time()
        self.step_times = []
        
        # 文件路径配置
        self.p1 = '/Users/<USER>/Desktop/2025年项目资料/信息化项目里程碑及交付物标准 20240801_v4.xlsx'
        self.fileDF = 'file0616.xlsx'
        self.profile = '/Users/<USER>/Documents/近三年投资计划/2018年至今项目清单（信息化项目）.xlsx'
        self.htp = '/Users/<USER>/Documents/科室项目管理周报/合同导数数据/合同查询导出_20250610143721870.xlsx'
        
        # 缓存数据
        self._cached_data = {}
        
    def log_time(self, step_name: str):
        """记录每个步骤的执行时间"""
        current_time = time.time()
        elapsed = current_time - self.start_time
        self.step_times.append((step_name, elapsed))
        print(f"{step_name}: {elapsed:.2f} seconds")
        self.start_time = current_time

    @lru_cache(maxsize=1000)
    def remove_punctuation_cached(self, text: str) -> str:
        """缓存版本的标点符号移除函数"""
        if not text:
            return ""
        
        # 预编译的正则表达式，比字符串操作更快
        punctuation_pattern = re.compile(r'[，。、；：""''（）《》【】—…·～！？　 \n\r\t()\[\]{}!@#$%^&*\-_=+|\\:;"\'<>,.?0-9`~·！￥…（）《》【】——]+')
        return punctuation_pattern.sub('', text)

    def reduce_memory_usage(self, df: pd.DataFrame, verbose: bool = False) -> pd.DataFrame:
        """优化内存使用"""
        numerics = ["int8", "int16", "int32", "int64", "float16", "float32", "float64"]
        start_mem = df.memory_usage(deep=True).sum() / 1024 ** 2
        
        for col in df.columns:
            col_type = df[col].dtypes
            if col_type in numerics:
                c_min = df[col].min()
                c_max = df[col].max()
                if str(col_type)[:3] == "int":
                    if c_min > np.iinfo(np.int8).min and c_max < np.iinfo(np.int8).max:
                        df[col] = df[col].astype(np.int8)
                    elif c_min > np.iinfo(np.int16).min and c_max < np.iinfo(np.int16).max:
                        df[col] = df[col].astype(np.int16)
                    elif c_min > np.iinfo(np.int32).min and c_max < np.iinfo(np.int32).max:
                        df[col] = df[col].astype(np.int32)
                else:
                    if c_min > np.finfo(np.float16).min and c_max < np.finfo(np.float16).max:
                        df[col] = df[col].astype(np.float16)
                    elif c_min > np.finfo(np.float32).min and c_max < np.finfo(np.float32).max:
                        df[col] = df[col].astype(np.float32)
        
        # 优化字符串列
        for col in df.select_dtypes(include=['object']):
            if df[col].dtype == 'object':
                try:
                    df[col] = df[col].astype('category')
                except:
                    pass
        
        end_mem = df.memory_usage(deep=True).sum() / 1024 ** 2
        if verbose:
            print(f"Memory usage decreased to {end_mem:.2f} MB ({100 * (start_mem - end_mem) / start_mem:.1f}% reduction)")
        
        return df

    def load_data_optimized(self):
        """优化的数据加载函数"""
        print("开始加载数据...")
        
        # 使用字典缓存Excel文件，避免重复读取
        excel_cache = {}
        
        def read_excel_cached(file_path, **kwargs):
            cache_key = (file_path, str(kwargs))
            if cache_key not in excel_cache:
                excel_cache[cache_key] = pd.read_excel(file_path, engine='calamine', **kwargs)
            return excel_cache[cache_key]
        
        # 批量读取标准数据
        standard_sheets = [
            ('1_信息系统建设与升级改造', '信息化'),
            ('4_运维维护', '信息维护修理'),
            ('4_运维维护', '数据资产维护'),
            ('2_信息基础设施建设与升级项目', None),
            ('3_信息安全体系建设与升级项目', None),
            ('5_信息专题研究项目', '专题研究')
        ]
        
        type2biaozhun = {}
        for sheet_name, type_name in standard_sheets:
            try:
                data = read_excel_cached(self.p1, sheet_name=sheet_name, skiprows=[0])['交付物名称'].dropna().tolist()
                if type_name:
                    type2biaozhun[type_name] = data
            except Exception as e:
                print(f"读取工作表 {sheet_name} 失败: {e}")
        
        self.log_time("标准数据加载")
        
        # 优化项目数据读取
        try:
            # 一次性读取所有需要的列
            profile_data1 = read_excel_cached(self.profile, sheet_name='历年投资计划')[
                ['项目名称', '项目编码', '类型', '分部', '项目负责人', '投资年份']
            ].dropna(subset=['项目名称'])
            
            profile_data2 = read_excel_cached(self.profile, sheet_name='历年信息维护修理')[
                ['合同名称', '合同编号', '类型', '分部', '项目负责人']
            ].dropna(subset=['合同名称']).rename(columns={'合同名称': '项目名称', '合同编号': '项目编码'})
            
            # 过滤年份
            yearss = ['2022年年初', '2022年年中', '2023年年初', '2023年年中', '2024年年初', '2024年年中', '2025年年初']
            profile_data1 = profile_data1[profile_data1['投资年份'].isin(yearss)]
            
            # 合并项目数据
            ptypeall = pd.concat([profile_data1, profile_data2], axis=0, ignore_index=True)
            ptypeall = self.reduce_memory_usage(ptypeall)
            
        except Exception as e:
            print(f"读取项目数据失败: {e}")
            ptypeall = pd.DataFrame()
        
        self.log_time("项目数据加载")
        
        # 读取合同数据
        try:
            htdf = read_excel_cached(self.htp)['合同编号'].dropna().tolist()
            htdf2 = profile_data2['项目编码'].dropna().tolist()
        except Exception as e:
            print(f"读取合同数据失败: {e}")
            htdf, htdf2 = [], []
        
        # 读取主文件数据
        try:
            data00 = read_excel_cached(self.fileDF, sheet_name='2025-06-13')
            data00 = self.reduce_memory_usage(data00)
        except Exception as e:
            print(f"读取主文件失败: {e}")
            data00 = pd.DataFrame()
        
        self.log_time("主文件数据加载")
        
        return type2biaozhun, ptypeall, htdf, htdf2, data00

    @njit
    def dp_lcs_len_numba(self, a, b):
        """Numba优化的最长公共子序列长度计算"""
        m, n = len(a), len(b)
        if m == 0 or n == 0:
            return 0
        
        # 使用滚动数组优化空间复杂度
        prev = np.zeros(n + 1, dtype=np.int32)
        curr = np.zeros(n + 1, dtype=np.int32)
        
        for i in range(m):
            for j in range(n):
                if a[i] == b[j]:
                    curr[j + 1] = prev[j] + 1
                else:
                    curr[j + 1] = max(prev[j + 1], curr[j])
            prev, curr = curr, prev
        
        return prev[n]

    def calculate_similarity(self, text1: str, text2: str) -> int:
        """计算两个文本的相似度"""
        if not text1 or not text2:
            return 0
        
        # 转换为字节数组进行比较
        try:
            a_bytes = np.frombuffer(text1.encode('utf-8'), dtype=np.uint8)
            b_bytes = np.frombuffer(text2.encode('utf-8'), dtype=np.uint8)
            return self.dp_lcs_len_numba(a_bytes, b_bytes)
        except:
            return 0

    def process_single_match(self, args: Tuple) -> Tuple:
        """处理单个匹配任务"""
        b_item, a_list_cleaned, a_list_raw = args
        project, text = b_item
        
        # 优化的文本分割
        marks = ["-", " ", "_", "、", "：", "项目", "电网", "（", "）", "(", ")", "盖章", "广州供电局", "广州局", "广东电网有限责任公司"]
        j_list = [text]
        
        for mark in marks:
            new_list = []
            for item in j_list:
                if mark in item:
                    new_list.extend(item.split(mark))
                else:
                    new_list.append(item)
            j_list = new_list
        
        max_len, target_idx = 0, -1
        
        for k in j_list:
            k_clean = self.remove_punctuation_cached(k)
            if not k_clean or len(k_clean) < 2:
                continue
                
            for i_idx, i_clean in enumerate(a_list_cleaned):
                if not i_clean or len(i_clean) < 2:
                    continue
                    
                length = self.calculate_similarity(i_clean, k_clean)
                if length > max_len:
                    target_idx = i_idx
                    max_len = length
        
        if max_len > 1:
            return (a_list_raw[target_idx], (text, project), max_len)
        else:
            return None

    def get_match_files_optimized(self, a_list: List[str], b_list: List[Tuple], max_workers: int = None) -> Tuple[Dict, List]:
        """优化的文件匹配函数"""
        if not max_workers:
            max_workers = min(mp.cpu_count(), 8)
        
        # 预处理a_list
        a_list_cleaned = [self.remove_punctuation_cached(i) for i in a_list]
        
        # 准备任务参数
        tasks = [(b_item, a_list_cleaned, a_list) for b_item in b_list]
        
        a2b_dict = defaultdict(list)
        no_match_b = []
        
        # 使用进程池进行并行处理
        with ProcessPoolExecutor(max_workers=max_workers) as executor:
            results = list(executor.map(self.process_single_match, tasks))
        
        for b_item, result in zip(b_list, results):
            if result:
                a_target, match_pair, match_len = result
                a2b_dict[a_target].append(match_pair)
            else:
                no_match_b.append(b_item)
        
        return dict(a2b_dict), no_match_b

    def process_data_optimized(self, data00: pd.DataFrame, ptypeall: pd.DataFrame, htdf: List, htdf2: List) -> pd.DataFrame:
        """优化的数据处理函数"""
        print("开始处理数据...")

        # 预处理文件路径
        data00['文件路径'] = data00['文件路径'].str.replace(r'^L:\\审计提资项目资料\\', '', regex=True)
        data00['文件名'] = data00['文件路径'].str.rsplit('\\', n=1).str.get(-1)

        # 优化正则表达式匹配
        dept_pattern = '|'.join(['数字应用部', '数据运营部', '安全运行部', '平台运维部', '架构与创新部', '信息调度与服务部'])
        htdf_pattern = '|'.join(htdf + htdf2) if htdf or htdf2 else ''
        pronum_pattern = '|'.join(ptypeall['项目编码'].dropna().astype(str).tolist())

        # 批量匹配，避免多次循环
        if htdf_pattern:
            data00['合同名称'] = data00['文件路径'].str.extract(f'({htdf_pattern})', expand=False)
        else:
            data00['合同名称'] = ''

        data00['分部'] = data00['文件路径'].str.extract(f'({dept_pattern})', expand=False)

        if pronum_pattern:
            data00['项目编码'] = data00['文件路径'].str.extract(f'({pronum_pattern})', expand=False)
        else:
            data00['项目编码'] = ''

        # 项目名称匹配优化
        proname_list = ptypeall['项目名称'].dropna().tolist()
        data00['项目名称'] = ''

        # 使用向量化操作进行匹配
        for proname in proname_list:
            mask = data00['文件路径'].str.contains(re.escape(proname), na=False)
            data00.loc[mask & (data00['项目名称'] == ''), '项目名称'] = proname

        # 合并项目信息
        result_df = pd.merge(data00, ptypeall, on='项目名称', how='left', suffixes=('', '_y'))

        # 清理和重命名列
        result_df = result_df.drop_duplicates('文件路径')
        result_df['文件大小'] = result_df['文件大小'] / 10000

        # 选择需要的列
        final_columns = ['文件路径', '文件大小', '文件类型', '分部', '合同名称', '项目编码', '项目名称', '类型', '项目负责人', '文件名']
        result_df = result_df[final_columns].fillna('')

        self.log_time("数据处理完成")
        return result_df

    def run_matching_process(self):
        """运行完整的匹配流程"""
        print("开始优化版文件匹配流程...")
        total_start = time.time()

        # 1. 加载数据
        type2biaozhun, ptypeall, htdf, htdf2, data00 = self.load_data_optimized()

        # 2. 处理数据
        processed_data = self.process_data_optimized(data00, ptypeall, htdf, htdf2)

        # 3. 执行匹配
        print("开始执行文件匹配...")
        match_start = time.time()

        all_results = {}
        nomatch_files = []

        # 按类型分组处理，减少重复计算
        type_groups = processed_data.groupby('类型')

        for match_type, group_data in type_groups:
            if not match_type or match_type not in type2biaozhun:
                continue

            print(f"处理类型: {match_type}, 文件数量: {len(group_data)}")

            a_list = type2biaozhun[match_type]  # 标准列表
            b_list = [(row['项目名称'], row['文件名']) for _, row in group_data.iterrows()]

            if not b_list:
                continue

            # 执行匹配
            a2b_dict, no_match_b = self.get_match_files_optimized(a_list, b_list)

            # 合并结果
            for a_key, matches in a2b_dict.items():
                if a_key not in all_results:
                    all_results[a_key] = []
                all_results[a_key].extend(matches)

            nomatch_files.extend(no_match_b)

        self.log_time("匹配执行完成")

        # 4. 生成最终结果
        print("生成最终结果...")
        records = []
        for category, items in all_results.items():
            for filename, project in items:
                records.append({
                    '交付物标准': category,
                    '文件名': filename,
                    '项目名称': project
                })

        if records:
            df_matched = pd.DataFrame(records)

            # 创建项目清单
            project_standards = []
            for type_name, standards in type2biaozhun.items():
                for standard in standards:
                    project_standards.append({'类型': type_name, '交付物标准': standard})

            df_standards = pd.DataFrame(project_standards)
            df_projects = processed_data[['类型', '项目名称']].drop_duplicates('项目名称')

            # 合并数据
            project_list = pd.merge(df_projects, df_standards, on='类型', how='left')
            final_result = pd.merge(project_list, df_matched, on=['项目名称', '交付物标准'], how='left')
            final_result = pd.merge(final_result, processed_data, on=['项目名称', '文件名'], how='left')
            final_result = pd.merge(final_result, ptypeall[['项目名称', '分部', '项目负责人', '项目编码']],
                                  on='项目名称', how='left', suffixes=('', '_final'))

            # 清理重复列
            final_result = final_result.drop_duplicates(['项目名称', '文件路径', '交付物标准'])

        else:
            final_result = pd.DataFrame()

        # 5. 保存结果
        output_file = '优化版匹配结果.xlsx'
        try:
            with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
                if not final_result.empty:
                    final_result.to_excel(writer, sheet_name='已匹配结果', index=False)
                processed_data.to_excel(writer, sheet_name='处理后数据', index=False)

                # 保存未匹配文件
                if nomatch_files:
                    nomatch_df = pd.DataFrame(nomatch_files, columns=['项目名称', '文件名'])
                    nomatch_df.to_excel(writer, sheet_name='未匹配文件', index=False)

            print(f"结果已保存到: {output_file}")
        except Exception as e:
            print(f"保存文件失败: {e}")

        # 输出统计信息
        total_time = time.time() - total_start
        print(f"\n=== 性能统计 ===")
        print(f"总执行时间: {total_time:.2f} 秒")
        print(f"处理文件数量: {len(processed_data)}")
        print(f"匹配成功数量: {len(records)}")
        print(f"未匹配数量: {len(nomatch_files)}")
        print(f"匹配成功率: {len(records)/(len(records)+len(nomatch_files))*100:.1f}%" if (len(records)+len(nomatch_files)) > 0 else "0%")

        # 显示各步骤耗时
        print(f"\n=== 各步骤耗时 ===")
        for step_name, elapsed_time in self.step_times:
            print(f"{step_name}: {elapsed_time:.2f} 秒")

        return final_result


def main():
    """主函数"""
    print("启动优化版文件匹配系统...")

    # 创建匹配器实例
    matcher = OptimizedFileMatching()

    # 运行匹配流程
    try:
        result = matcher.run_matching_process()
        print("匹配流程完成!")
        return result
    except Exception as e:
        print(f"执行过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None


if __name__ == "__main__":
    # 设置多进程启动方法
    mp.set_start_method('spawn', force=True)

    # 运行主程序
    result = main()

    # 强制垃圾回收
    gc.collect()
