import pandas as pd

# 您的原始代码
dd1 = pd.read_excel(pp1, sheet_name='2025年B类新建', skiprows=[0,1,2])
print(dd1.head(5))

dd1项目名称 = dd1[['项目名称','项目编码']].dropna().values.tolist()
print(dd1项目名称)

ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]
data = [i for i in ll1项目名称]

# 指定的列
desired_cols = ["标段名称", "标段编号", "合同名称","合同编号","合同金额","中标厂家",'是否跨项目签合同','本标段对应合同金额']

# 获取实际存在的列（前3个）
available_cols = [col for col in desired_cols if col in dd1.columns][:3]

# 直接替换range(3)
details = {
    row: {
        "rows": [
            {"name": available_cols[j] if j < len(available_cols) else f"字段{j+1}", 
             "value": str(dd1.iloc[i][available_cols[j]]) if j < len(available_cols) and pd.notna(dd1.iloc[i][available_cols[j]]) else ""}
            for j in range(3)
        ]
    }
    for i, row in enumerate(data)
}

print(f"使用的列: {available_cols}")
print("结果:")
for key, value in list(details.items())[:2]:
    print(f"{key}: {value}")
