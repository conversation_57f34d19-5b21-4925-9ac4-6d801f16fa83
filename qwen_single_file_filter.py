import pandas as pd
from openai import OpenAI
import os
import json
import time
from tqdm import tqdm

# 读取数据路径
p1 = '/Users/<USER>/Desktop/2025年项目资料/file0607.xlsx'
p2 = '/Users/<USER>/Documents/近三年投资计划/2018年至今项目清单（信息化项目）.xlsx'

# 读取文件名和项目名称列表
filenames = pd.read_excel(p1, sheet_name='2025-06-05', engine='calamine')['文件名'].astype('str').tolist()
pronames1 = pd.read_excel(p2, sheet_name='历年投资计划', engine='calamine')['项目名称'].astype('str').tolist()
pronames2 = pd.read_excel(p2, sheet_name='历年信息维护修理', engine='calamine')['合同名称'].astype('str').tolist()
pronames3 = pronames1 + pronames2

print(f"加载了 {len(filenames)} 个文件名")
print(f"加载了 {len(pronames3)} 个项目关键词")

client = OpenAI(
    api_key="sk-e1bfa2386d234bdf9498f84804075cce",
    base_url="https://dashscope.aliyuncs.com/compatible-mode/v1",
)

def process_single_filename(filename, keywords):
    """处理单个文件名"""
    # 限制关键词数量，选择最相关的关键词
    limited_keywords = keywords[:100]  # 限制关键词数量
    keywords_text = "、".join(limited_keywords)
    
    prompt = f"""请分析以下文件名，根据项目关键词列表删除其中匹配的关键词部分。

文件名：{filename}

项目关键词列表：{keywords_text}

分析要求：
1. 识别文件名中包含的项目关键词（支持模糊匹配、部分匹配、同义词）
2. 删除匹配的关键词部分，保留有意义的剩余部分
3. 保持文件扩展名不变
4. 如果没有匹配的关键词，保持原文件名

请返回JSON格式结果：
{{
    "original_filename": "原始文件名",
    "matched_keywords": ["匹配的关键词1", "匹配的关键词2"],
    "filtered_filename": "过滤后的文件名",
    "has_match": true/false,
    "explanation": "处理说明"
}}

只返回JSON格式结果，不要添加其他文字。"""
    
    try:
        response = client.chat.completions.create(
            model="qwen-plus",
            messages=[
                {"role": "system", "content": "你是一个专业的文件名处理专家。"},
                {"role": "user", "content": prompt},
            ],
            temperature=0.1,
            max_tokens=1000,
            stream=False
        )
        
        response_text = response.choices[0].message.content.strip()
        
        try:
            result = json.loads(response_text)
            return result
        except json.JSONDecodeError:
            # JSON解析失败，返回默认结果
            return {
                "original_filename": filename,
                "matched_keywords": [],
                "filtered_filename": filename,
                "has_match": False,
                "explanation": f"JSON解析失败: {response_text[:100]}..."
            }
    
    except Exception as e:
        return {
            "original_filename": filename,
            "matched_keywords": [],
            "filtered_filename": filename,
            "has_match": False,
            "explanation": f"API调用失败: {str(e)}"
        }

# 处理所有文件名
all_results = []
total_files = len(filenames)

print(f"开始逐个处理 {total_files} 个文件名...")

for i, filename in enumerate(tqdm(filenames, desc="处理文件名")):
    if i % 50 == 0:  # 每50个文件显示一次进度
        print(f"\n处理进度: {i+1}/{total_files}")
    
    # 处理单个文件名
    result = process_single_filename(filename, pronames3)
    
    # 添加序号
    result["序号"] = i + 1
    all_results.append(result)
    
    # 显示有变化的结果
    if result.get("has_match", False):
        print(f"  ✓ {result['original_filename']} -> {result['filtered_filename']}")
        if result.get("matched_keywords"):
            print(f"    匹配关键词: {', '.join(result['matched_keywords'])}")
    
    # 添加延迟避免API限制
    time.sleep(0.2)  # 200ms延迟

# 创建结果DataFrame
results_df = pd.DataFrame(all_results)

# 重新排列列顺序
column_order = [
    "序号",
    "original_filename", 
    "filtered_filename", 
    "has_match", 
    "matched_keywords",
    "explanation"
]

results_df = results_df[column_order]

# 重命名列为中文
results_df.columns = [
    "序号",
    "原始文件名",
    "过滤后文件名", 
    "是否有匹配",
    "匹配的关键词",
    "处理说明"
]

# 保存详细结果
detailed_output = "qwen_single_filter_detailed.xlsx"
results_df.to_excel(detailed_output, index=False, engine='openpyxl')

# 创建简化版本 - 只包含过滤后的文件名
filtered_filenames = [result["filtered_filename"] for result in all_results]
simple_df = pd.DataFrame({"过滤后的文件名": filtered_filenames})
simple_output = "qwen_filtered_filenames_simple.xlsx"
simple_df.to_excel(simple_output, index=False, engine='openpyxl')

# 创建对比版本
comparison_data = []
for result in all_results:
    comparison_data.append({
        "序号": result["序号"],
        "原始文件名": result["original_filename"],
        "过滤后文件名": result["filtered_filename"],
        "是否有变化": result.get("has_match", False),
        "匹配关键词": ", ".join(result.get("matched_keywords", [])) if result.get("matched_keywords") else ""
    })

comparison_df = pd.DataFrame(comparison_data)
comparison_output = "qwen_filename_comparison_detailed.xlsx"
comparison_df.to_excel(comparison_output, index=False, engine='openpyxl')

# 输出统计信息
print(f"\n=== 处理完成 ===")
print(f"总文件数: {len(all_results)}")
match_count = len([r for r in all_results if r.get("has_match", False)])
success_count = len([r for r in all_results if "API调用失败" not in r.get("explanation", "")])

print(f"成功处理: {success_count}")
print(f"有关键词匹配的文件: {match_count}")
print(f"匹配率: {match_count/len(all_results)*100:.1f}%")
print(f"成功率: {success_count/len(all_results)*100:.1f}%")

print(f"\n=== 输出文件 ===")
print(f"详细结果: {detailed_output}")
print(f"仅过滤后文件名: {simple_output}")
print(f"对比结果: {comparison_output}")

# 显示匹配统计
matched_keywords_count = {}
for result in all_results:
    if result.get("matched_keywords"):
        for keyword in result["matched_keywords"]:
            matched_keywords_count[keyword] = matched_keywords_count.get(keyword, 0) + 1

if matched_keywords_count:
    print(f"\n=== 最常匹配的关键词 (前10个) ===")
    sorted_keywords = sorted(matched_keywords_count.items(), key=lambda x: x[1], reverse=True)
    for keyword, count in sorted_keywords[:10]:
        print(f"{keyword}: {count}次")

# 显示一些有变化的示例
print(f"\n=== 处理示例（有变化的文件名，前10个） ===")
changed_results = [r for r in all_results if r.get("has_match", False)][:10]
for i, result in enumerate(changed_results, 1):
    print(f"{i}. 原文件名: {result['original_filename']}")
    print(f"   过滤后: {result['filtered_filename']}")
    if result.get("matched_keywords"):
        print(f"   匹配关键词: {', '.join(result['matched_keywords'])}")
    print()

if not changed_results:
    print("没有文件名发生变化")
    print("建议检查：")
    print("1. 关键词列表是否正确")
    print("2. 文件名格式是否符合预期")
    print("3. 匹配策略是否需要调整")

print("处理完成！")
