import pandas as pd
from nicegui import ui

def load_project_data():
    """加载项目数据"""
    try:
        pp1 = '/Users/<USER>/Downloads/统计表(1).xlsx'
        dd1 = pd.read_excel(pp1, sheet_name='2023年B类新建', skiprows=[0, 2, 3])
        dd1项目名称 = dd1[['项目名称', '项目编码']].dropna().values.tolist()
        ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]
        data = [i for i in ll1项目名称]
        return data
    except Exception as e:
        print(f"加载数据失败: {e}")
        return []

def create_project_details(data):
    """创建项目详情"""
    details = {
        row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
        for row in data
    }
    return details

@ui.page("/")
def home():
    """主页面 - 不使用ex4nicegui"""
    ui.label("项目管理系统").classes("text-2xl font-bold mb-4")
    
    # 加载数据
    data = load_project_data()
    details = create_project_details(data)
    
    if not data:
        ui.label("没有数据").classes("text-red-500")
        return
    
    ui.label(f"共 {len(data)} 个项目").classes("text-green-600 mb-4")
    
    # 简单分页
    page_size = 10
    current_page = ui.state(0)
    total_pages = (len(data) - 1) // page_size + 1
    
    def get_current_data():
        start = current_page.value * page_size
        end = start + page_size
        return data[start:end]
    
    def next_page():
        if current_page.value < total_pages - 1:
            current_page.value += 1
            update_display()
    
    def prev_page():
        if current_page.value > 0:
            current_page.value -= 1
            update_display()
    
    # 分页控件
    with ui.row().classes("mb-4"):
        ui.button("上一页", on_click=prev_page).props("outline")
        ui.label().bind_text_from(current_page, lambda x: f"第 {x+1}/{total_pages} 页")
        ui.button("下一页", on_click=next_page).props("outline")
    
    # 内容容器
    content_container = ui.column().classes("w-full gap-2")
    
    def update_display():
        content_container.clear()
        with content_container:
            for row in get_current_data():
                with ui.expansion(row).classes("border rounded"):
                    if row in details:
                        with ui.grid(columns=2).classes("gap-2 p-2"):
                            ui.label("标段名称").classes("font-bold")
                            ui.label("项目总投资").classes("font-bold")
                            
                            for detail in details[row]["rows"]:
                                ui.label(detail["name"])
                                ui.label(detail["value"])
    
    # 初始显示
    update_display()

if __name__ == "__main__":
    ui.run()
