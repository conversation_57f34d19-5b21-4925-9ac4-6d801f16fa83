#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试DeepSeek处理器的基本功能
"""

import os
from deepseek_processor import DeepSeekProcessor
from sample_data import SAMPLE_TEXTS, SAMPLE_PROMPTS


def test_processor_initialization():
    """测试处理器初始化"""
    print("=== 测试处理器初始化 ===")
    
    try:
        # 检查API密钥是否设置
        api_key = os.getenv('DEEPSEEK_API_KEY')
        if not api_key:
            print("❌ DEEPSEEK_API_KEY 环境变量未设置")
            print("请在 .env 文件中设置你的DeepSeek API密钥")
            return False
        
        # 初始化处理器
        processor = DeepSeekProcessor()
        print("✅ 处理器初始化成功")
        return True
        
    except Exception as e:
        print(f"❌ 处理器初始化失败: {str(e)}")
        return False


def test_single_text_processing():
    """测试单个文本处理"""
    print("\n=== 测试单个文本处理 ===")
    
    try:
        processor = DeepSeekProcessor()
        test_text = "人工智能技术正在快速发展。"
        test_prompt = "请总结以下文本："
        
        print(f"测试文本: {test_text}")
        print(f"测试提示词: {test_prompt}")
        
        result = processor.process_single_text(test_text, test_prompt)
        
        if result['status'] == 'success':
            print("✅ 单个文本处理成功")
            print(f"处理结果: {result['processed_text']}")
            return True
        else:
            print(f"❌ 单个文本处理失败: {result.get('error', '未知错误')}")
            return False
            
    except Exception as e:
        print(f"❌ 测试过程中出错: {str(e)}")
        return False


def test_batch_processing():
    """测试批量处理"""
    print("\n=== 测试批量处理 ===")
    
    try:
        processor = DeepSeekProcessor()
        test_texts = SAMPLE_TEXTS[:3]  # 只测试前3个文本
        test_prompt = SAMPLE_PROMPTS['summarize']
        
        print(f"测试文本数量: {len(test_texts)}")
        print(f"测试提示词: {test_prompt}")
        
        results = processor.process_batch(test_texts, test_prompt)
        
        successful = sum(1 for r in results if r['status'] == 'success')
        print(f"✅ 批量处理完成，成功处理 {successful}/{len(test_texts)} 个文本")
        
        # 显示第一个结果
        if results and results[0]['status'] == 'success':
            print(f"示例结果: {results[0]['processed_text'][:100]}...")
        
        return successful > 0
        
    except Exception as e:
        print(f"❌ 批量测试过程中出错: {str(e)}")
        return False


def test_file_operations():
    """测试文件操作"""
    print("\n=== 测试文件操作 ===")
    
    try:
        processor = DeepSeekProcessor()
        test_texts = SAMPLE_TEXTS[:2]
        test_prompt = "请翻译以下文本："
        
        # 处理文本
        results = processor.process_batch(test_texts, test_prompt)
        
        # 保存结果
        output_file = "test_results.json"
        processor.save_results(results, output_file)
        
        # 检查文件是否创建
        if os.path.exists(output_file) and os.path.exists("test_results.csv"):
            print("✅ 文件保存成功")
            
            # 清理测试文件
            os.remove(output_file)
            os.remove("test_results.csv")
            print("✅ 测试文件已清理")
            return True
        else:
            print("❌ 文件保存失败")
            return False
            
    except Exception as e:
        print(f"❌ 文件操作测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("DeepSeek处理器功能测试")
    print("=" * 50)
    
    tests = [
        test_processor_initialization,
        test_single_text_processing,
        test_batch_processing,
        test_file_operations
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试 {test.__name__} 出现异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！程序可以正常使用。")
    else:
        print("⚠️  部分测试失败，请检查配置和网络连接。")


if __name__ == '__main__':
    main() 