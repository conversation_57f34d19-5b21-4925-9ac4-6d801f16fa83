from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable


pp1='/Users/<USER>/Downloads/统计表(1).xlsx'
dd1=pd.read_excel(pp1,sheet_name='2023年B类新建',skiprows=[0,2,3])
dd1项目名称 = dd1[['项目名称', '项目编码']].dropna().values.tolist()
ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]

data = [i for i in ll1项目名称]

details = {
        row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
        for row in data
    }
print(details)


def convert_to_simple_tree_data(data, details):
    """
    转换为简单的树形数据（避免函数序列化问题）
    """
    tree_data = []

    for project in data:
        # 父节点 - 项目（手动添加样式）
        tree_data.append({
            "id": f"project_{len(tree_data)}",
            "项目名称": f"📁 {project}",  # 项目用文件夹图标
            "标段名称": "",
            "项目总投资": "",
            "type": "project",
            "level": 0,
            "expanded": True
        })

        # 子节点 - 标段（手动缩进）
        if project in details:
            for detail in details[project]["rows"]:
                tree_data.append({
                    "id": f"detail_{len(tree_data)}",
                    "项目名称": f"　　📄 {detail['name']}",  # 标段用缩进+文件图标
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "type": "detail",
                    "level": 1,
                    "expanded": False
                })

    return tree_data

@ui.page("/")
def home():
    # 生成tree_data
    tree_data = convert_to_simple_tree_data(data, details)
    
    # 转换数据
    summary_data = rxui.use_pagination(tree_data, page_size=100)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=10")

        # 完全移除所有函数的AgGrid配置
        ui.aggrid({
            "columnDefs": [
                {
                    "headerName": "项目名称",
                    "field": "项目名称",
                    "width": 400
                    # 完全移除cellStyle - 不使用任何函数
                },
                {
                    "headerName": "标段名称",
                    "field": "标段名称",
                    "width": 200
                },
                {
                    "headerName": "项目总投资",
                    "field": "项目总投资",
                    "width": 200
                }
            ],
            "rowData": summary_data.current_source.value,
            "defaultColDef": {
                "resizable": True,
                "sortable": True,
                "filter": True
            },
            "rowHeight": 40,
            "suppressRowHoverHighlight": False,
            "rowSelection": "single"
        }).classes("w-full h-[500px] border-2 border-gray-300")


ui.run()
