# 直接替换您的原始代码

@ui.page("/")
def home():
    # 将原始数据转换为AgGrid树形格式
    def convert_to_tree_data(data, details):
        tree_rows = []
        for project in data:
            # 添加项目行（父节点）
            tree_rows.append({
                "id": f"project_{len(tree_rows)}",
                "项目名称": project,
                "标段名称": "",
                "项目总投资": "",
                "level": 0,
                "isParent": True
            })
            
            # 添加标段行（子节点）
            if project in details:
                for detail in details[project]["rows"]:
                    tree_rows.append({
                        "id": f"detail_{len(tree_rows)}",
                        "项目名称": "",
                        "标段名称": detail["name"],
                        "项目总投资": detail["value"],
                        "level": 1,
                        "isParent": False,
                        "parent": project
                    })
        return tree_rows
    
    # 转换数据
    tree_data = convert_to_tree_data(data, details)
    summary_data = rxui.use_pagination(tree_data, page_size=100)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=10")

        # 使用AgGrid替换原来的expansion + table
        ui.aggrid({
            "columnDefs": [
                {
                    "headerName": "项目名称",
                    "field": "项目名称",
                    "width": 400,
                    "cellStyle": lambda params: {
                        "fontWeight": "bold" if params['data'].get('level') == 0 else "normal",
                        "paddingLeft": f"{params['data'].get('level', 0) * 20 + 10}px"
                    }
                },
                {
                    "headerName": "标段名称",
                    "field": "标段名称",
                    "width": 200
                },
                {
                    "headerName": "项目总投资",
                    "field": "项目总投资",
                    "width": 200
                }
            ],
            "rowData": summary_data.current_source.value,
            "defaultColDef": {
                "resizable": True,
                "sortable": True,
                "filter": True
            },
            "rowHeight": 40,
            "suppressRowHoverHighlight": False,
            "rowSelection": "single"
        }).classes("w-full h-[500px] border-2 border-gray-300")


ui.run()
