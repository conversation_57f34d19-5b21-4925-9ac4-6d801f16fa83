from pathlib import Path
import pandas as pd
from nicegui import ui
from ex4nicegui import rxui, effect_refreshable


pp1='/Users/<USER>/Downloads/统计表(1).xlsx'
dd1=pd.read_excel(pp1,sheet_name='2023年B类新建',skiprows=[0,2,3])
dd1项目名称 = dd1[['项目名称', '项目编码']].dropna().values.tolist()
ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]

data = [i for i in ll1项目名称]

details = {
        row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
        for row in data
    }

# 全局状态：记录每个项目的展开状态
expanded_projects = {project: False for project in data}

def get_visible_tree_data(data, details, expanded_projects):
    """根据展开状态生成可见的树形数据"""
    tree_data = []
    
    for project in data:
        # 项目行（始终显示）
        is_expanded = expanded_projects.get(project, False)
        expand_icon = "📂" if is_expanded else "📁"  # 展开/折叠图标
        
        tree_data.append({
            "id": f"project_{project}",
            "项目名称": f"{expand_icon} {project}",
            "标段名称": "",
            "项目总投资": "",
            "type": "project",
            "level": 0,
            "project_key": project,
            "is_expanded": is_expanded,
            "clickable": True
        })
        
        # 子节点（只有展开时才显示）
        if is_expanded and project in details:
            for i, detail in enumerate(details[project]["rows"]):
                tree_data.append({
                    "id": f"detail_{project}_{i}",
                    "项目名称": f"　　📄 {detail['name']}",
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "type": "detail",
                    "level": 1,
                    "project_key": project,
                    "is_expanded": False,
                    "clickable": False
                })
    
    return tree_data

@ui.page("/")
def home():
    """可展开折叠的树形表格"""
    
    ui.label("可展开折叠的树形表格").classes("text-xl font-bold mb-4")
    ui.label("点击项目名称前的文件夹图标来展开/折叠").classes("text-gray-600 mb-4")
    
    # 初始数据
    tree_data = get_visible_tree_data(data, details, expanded_projects)
    summary_data = rxui.use_pagination(tree_data, page_size=100)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=10")

        def handle_row_click(event):
            """处理行点击事件"""
            if event and 'data' in event:
                row_data = event['data']
                if row_data.get('type') == 'project' and row_data.get('clickable'):
                    project_key = row_data.get('project_key')
                    if project_key:
                        # 切换展开状态
                        expanded_projects[project_key] = not expanded_projects[project_key]
                        
                        # 重新生成数据
                        new_tree_data = get_visible_tree_data(data, details, expanded_projects)
                        summary_data.set_source(new_tree_data)
                        
                        ui.notify(f"{'展开' if expanded_projects[project_key] else '折叠'}: {project_key}")

        # AgGrid配置
        grid = ui.aggrid({
            "columnDefs": [
                {
                    "headerName": "项目名称",
                    "field": "项目名称",
                    "width": 400,
                    "suppressMenu": True
                },
                {
                    "headerName": "标段名称",
                    "field": "标段名称",
                    "width": 200
                },
                {
                    "headerName": "项目总投资",
                    "field": "项目总投资",
                    "width": 200
                }
            ],
            "rowData": summary_data.current_source.value,
            "defaultColDef": {
                "resizable": True,
                "sortable": True,
                "filter": True
            },
            "rowHeight": 40,
            "suppressRowHoverHighlight": False,
            "rowSelection": "single",
            "onRowClicked": handle_row_click
        }).classes("w-full h-[500px] border-2 border-gray-300")

@ui.page("/expansion")
def expansion_version():
    """使用NiceGUI expansion组件的版本"""
    
    ui.label("使用Expansion组件的树形结构").classes("text-xl font-bold mb-4")
    
    # 分页处理
    summary_data = rxui.use_pagination(data, page_size=20)

    @effect_refreshable.on(summary_data)
    def on_summary_data_changed():
        summary_data.create_q_pagination().props("max-pages=5")

        with ui.column().classes("gap-1 w-full"):
            for project in summary_data.current_source.value:
                with ui.expansion(text=f"📁 {project}").classes("border border-gray-200 rounded"):
                    if project in details:
                        # 创建标段表格
                        ui.table(
                            rows=details[project]["rows"],
                            columns=[
                                {"name": "name", "field": "name", "label": "标段名称"},
                                {"name": "value", "field": "value", "label": "项目总投资"},
                            ],
                        ).classes("w-full")

@ui.page("/manual")
def manual_expand():
    """手动控制展开的版本"""
    
    ui.label("手动控制展开版本").classes("text-xl font-bold mb-4")
    
    # 创建展开状态的响应式变量
    expand_states = {project: ui.state(False) for project in data[:10]}  # 只显示前10个项目
    
    with ui.column().classes("gap-2 w-full"):
        for project in data[:10]:
            with ui.card().classes("w-full"):
                # 项目标题行
                with ui.row().classes("w-full items-center"):
                    # 展开/折叠按钮
                    expand_btn = ui.button(
                        icon="expand_more" if not expand_states[project].value else "expand_less",
                        on_click=lambda p=project: toggle_expand(p)
                    ).props("flat round size=sm")
                    
                    # 项目名称
                    ui.label(f"📁 {project}").classes("text-lg font-medium flex-grow")
                
                # 标段内容（条件显示）
                detail_container = ui.column().classes("w-full mt-2")
                
                def toggle_expand(proj):
                    expand_states[proj].value = not expand_states[proj].value
                    expand_btn.props(f"icon={'expand_less' if expand_states[proj].value else 'expand_more'}")
                    update_details(proj)
                
                def update_details(proj):
                    detail_container.clear()
                    if expand_states[proj].value and proj in details:
                        with detail_container:
                            ui.table(
                                rows=details[proj]["rows"],
                                columns=[
                                    {"name": "name", "field": "name", "label": "标段名称"},
                                    {"name": "value", "field": "value", "label": "项目总投资"},
                                ],
                            ).classes("w-full")

@ui.page("/buttons")
def button_version():
    """使用按钮控制的版本"""
    
    ui.label("按钮控制版本").classes("text-xl font-bold mb-4")
    
    # 全局控制按钮
    with ui.row().classes("mb-4"):
        ui.button("全部展开", on_click=lambda: expand_all(True)).classes("bg-green-500 text-white")
        ui.button("全部折叠", on_click=lambda: expand_all(False)).classes("bg-red-500 text-white")
    
    def expand_all(expand):
        for project in expanded_projects:
            expanded_projects[project] = expand
        refresh_data()
    
    def refresh_data():
        new_tree_data = get_visible_tree_data(data, details, expanded_projects)
        # 这里需要更新显示，实际实现中需要重新渲染
        ui.notify(f"已{'展开' if any(expanded_projects.values()) else '折叠'}所有项目")
    
    # 显示当前状态
    with ui.column().classes("gap-2 w-full"):
        for project in data[:5]:  # 只显示前5个项目
            with ui.row().classes("items-center gap-2"):
                ui.button(
                    "📂" if expanded_projects[project] else "📁",
                    on_click=lambda p=project: toggle_project(p)
                ).props("flat")
                
                ui.label(project).classes("flex-grow")
                
                ui.label(f"({len(details[project]['rows'])} 个标段)" if project in details else "").classes("text-gray-500")
    
    def toggle_project(project):
        expanded_projects[project] = not expanded_projects[project]
        ui.notify(f"{'展开' if expanded_projects[project] else '折叠'}: {project}")

if __name__ == "__main__":
    # 添加导航
    with ui.header():
        ui.label("可展开树形结构").classes("text-white text-lg font-bold")
        with ui.row():
            ui.link("AgGrid版", "/").classes("text-white")
            ui.link("Expansion版", "/expansion").classes("text-white ml-4")
            ui.link("手动版", "/manual").classes("text-white ml-4")
            ui.link("按钮版", "/buttons").classes("text-white ml-4")
    
    ui.run()
