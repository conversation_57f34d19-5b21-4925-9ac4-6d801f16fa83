#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Excel保存功能
"""

import pandas as pd
import numpy as np
from datetime import datetime

def test_excel_save():
    """测试Excel保存功能"""
    print("测试Excel保存功能...")
    
    # 创建测试数据
    test_data = {
        '文件名': ['测试文件1.docx', '测试文件2.pdf', '测试文件3.xlsx'],
        '项目名称': ['测试项目A', '测试项目B', '测试项目C'],
        '文件路径': ['路径1/测试文件1.docx', '路径2/测试文件2.pdf', '路径3/测试文件3.xlsx'],
        '文件大小': [1.5, 2.3, 0.8],
        '文件类型': ['Word文档', 'PDF文档', 'Excel文档'],
        '分部': ['数字应用部', '数据运营部', '安全运行部'],
        '项目负责人': ['张三', '李四', '王五'],
        '合同名称': ['合同001', '合同002', '合同003'],
        '项目编码': ['PRJ001', 'PRJ002', 'PRJ003']
    }
    
    match_records = [
        {'交付物标准': '需求分析报告', '文件名': '测试文件1.docx', '项目名称': '测试项目A'},
        {'交付物标准': '设计文档', '文件名': '测试文件2.pdf', '项目名称': '测试项目B'},
        {'交付物标准': '测试报告', '文件名': '测试文件3.xlsx', '项目名称': '测试项目C'}
    ]
    
    no_matches = [
        ('未匹配项目', '未匹配文件.txt')
    ]
    
    type2biaozhun = {
        '信息化': ['需求分析报告', '设计文档', '测试报告'],
        '信息维护修理': ['维护手册', '操作指南']
    }
    
    # 创建DataFrame
    processed_data = pd.DataFrame(test_data)
    
    # 保存到Excel
    output_file = '测试结果.xlsx'
    
    try:
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            
            # 1. 详细匹配结果
            detailed_matches = []
            for record in match_records:
                file_detail = processed_data[
                    (processed_data['项目名称'] == record['项目名称']) & 
                    (processed_data['文件名'] == record['文件名'])
                ]
                
                if not file_detail.empty:
                    detail_record = {
                        '交付物标准': record['交付物标准'],
                        '文件名': record['文件名'],
                        '项目名称': record['项目名称'],
                        '文件路径': file_detail.iloc[0]['文件路径'],
                        '文件大小(万字节)': file_detail.iloc[0]['文件大小'],
                        '文件类型': file_detail.iloc[0]['文件类型'],
                        '分部': file_detail.iloc[0]['分部'],
                        '项目负责人': file_detail.iloc[0]['项目负责人'],
                        '合同名称': file_detail.iloc[0]['合同名称'],
                        '项目编码': file_detail.iloc[0]['项目编码']
                    }
                    detailed_matches.append(detail_record)
            
            if detailed_matches:
                detailed_df = pd.DataFrame(detailed_matches)
                detailed_df.to_excel(writer, sheet_name='详细匹配结果', index=False)
            
            # 2. 匹配结果汇总
            match_df = pd.DataFrame(match_records)
            match_df.to_excel(writer, sheet_name='匹配结果汇总', index=False)
            
            # 3. 处理后数据
            processed_data.to_excel(writer, sheet_name='处理后数据', index=False)
            
            # 4. 未匹配文件
            if no_matches:
                nomatch_df = pd.DataFrame(no_matches, columns=['项目名称', '文件名'])
                nomatch_df.to_excel(writer, sheet_name='未匹配文件', index=False)
            
            # 5. 交付物标准清单
            standards_list = []
            for type_name, standards in type2biaozhun.items():
                for standard in standards:
                    standards_list.append({
                        '类型': type_name,
                        '交付物标准': standard
                    })
            
            if standards_list:
                standards_df = pd.DataFrame(standards_list)
                standards_df.to_excel(writer, sheet_name='交付物标准清单', index=False)
            
            # 6. 统计汇总
            stats_data = [
                {'统计项目': '总文件数', '数量': len(processed_data)},
                {'统计项目': '匹配成功', '数量': len(match_records)},
                {'统计项目': '未匹配', '数量': len(no_matches)},
                {'统计项目': '匹配成功率(%)', '数量': round(len(match_records)/(len(match_records)+len(no_matches))*100, 1)},
                {'统计项目': '--- 按交付物标准统计 ---', '数量': ''},
                {'统计项目': '需求分析报告', '数量': 1},
                {'统计项目': '设计文档', '数量': 1},
                {'统计项目': '测试报告', '数量': 1}
            ]
            
            stats_df = pd.DataFrame(stats_data)
            stats_df.to_excel(writer, sheet_name='统计汇总', index=False)
        
        print(f"✅ 测试成功！结果已保存到: {output_file}")
        print(f"📊 包含以下工作表:")
        print(f"   - 详细匹配结果")
        print(f"   - 匹配结果汇总")
        print(f"   - 处理后数据")
        print(f"   - 未匹配文件")
        print(f"   - 交付物标准清单")
        print(f"   - 统计汇总")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_csv_save():
    """测试CSV保存功能（备选方案）"""
    print("\n测试CSV保存功能...")
    
    try:
        # 创建测试数据
        test_data = {
            '文件名': ['测试文件1.docx', '测试文件2.pdf'],
            '项目名称': ['测试项目A', '测试项目B'],
            '交付物标准': ['需求分析报告', '设计文档']
        }
        
        df = pd.DataFrame(test_data)
        df.to_csv('测试结果.csv', index=False, encoding='utf-8-sig')
        
        print("✅ CSV保存测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ CSV保存测试失败: {e}")
        return False

def check_dependencies():
    """检查依赖包"""
    print("检查依赖包...")
    
    required_packages = ['pandas', 'openpyxl', 'numpy']
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}: 已安装")
        except ImportError:
            print(f"❌ {package}: 未安装")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n需要安装以下包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def main():
    """主函数"""
    print("=== Excel保存功能测试 ===")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查依赖
    if not check_dependencies():
        return
    
    # 测试Excel保存
    excel_success = test_excel_save()
    
    # 测试CSV保存
    csv_success = test_csv_save()
    
    print(f"\n=== 测试结果 ===")
    print(f"Excel保存: {'✅ 成功' if excel_success else '❌ 失败'}")
    print(f"CSV保存: {'✅ 成功' if csv_success else '❌ 失败'}")
    
    if excel_success:
        print(f"\n🎉 Excel保存功能正常，可以运行主程序！")
    else:
        print(f"\n⚠️  Excel保存有问题，建议检查openpyxl包安装")

if __name__ == "__main__":
    main()
