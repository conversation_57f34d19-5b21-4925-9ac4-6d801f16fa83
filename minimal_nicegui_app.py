import pandas as pd
from nicegui import ui

# 全局变量存储数据
DATA = []
DETAILS = {}

def load_data():
    """加载数据函数"""
    global DATA, DETAILS
    
    try:
        print("开始加载数据...")
        
        # 文件路径
        pp1 = '/Users/<USER>/Downloads/统计表(1).xlsx'
        
        # 读取Excel
        dd1 = pd.read_excel(pp1, sheet_name='2023年B类新建', skiprows=[0, 2, 3])
        print(f"读取到数据: {dd1.shape}")
        
        # 处理数据
        dd1项目名称 = dd1[['项目名称', '项目编码']].dropna().values.tolist()
        ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]
        DATA = [i for i in ll1项目名称]
        
        # 创建详情
        DETAILS = {
            row: {"rows": [{"name": f"标段名称{j}", "value": f"{j}"} for j in range(3)]}
            for row in DATA
        }
        
        print(f"成功加载 {len(DATA)} 个项目")
        return True
        
    except Exception as e:
        print(f"加载失败: {e}")
        return False

@ui.page("/")
def home():
    """主页面"""
    ui.label("项目管理系统").classes("text-2xl mb-4")
    
    # 显示数据状态
    if not DATA:
        ui.label("⚠️ 暂无数据").classes("text-orange-500")
        if ui.button("加载数据"):
            if load_data():
                ui.notify("数据加载成功！", type='positive')
                ui.navigate.reload()
            else:
                ui.notify("数据加载失败！", type='negative')
        return
    
    ui.label(f"📊 共有 {len(DATA)} 个项目").classes("text-green-600 mb-4")
    
    # 简单列表显示（不使用分页）
    with ui.column().classes("w-full gap-2"):
        for i, row in enumerate(DATA[:10]):  # 只显示前10个
            with ui.expansion(f"{i+1}. {row}").classes("border rounded"):
                if row in DETAILS:
                    # 简单表格
                    with ui.grid(columns=2).classes("gap-2 p-2"):
                        ui.label("标段名称").classes("font-bold")
                        ui.label("项目总投资").classes("font-bold")
                        
                        for detail in DETAILS[row]["rows"]:
                            ui.label(detail["name"])
                            ui.label(detail["value"])

@ui.page("/simple")
def simple_page():
    """超简单页面"""
    ui.label("简单测试页面").classes("text-xl mb-4")
    
    ui.label(f"数据状态: {'已加载' if DATA else '未加载'}").classes("mb-2")
    ui.label(f"数据数量: {len(DATA)}").classes("mb-4")
    
    if ui.button("重新加载数据"):
        success = load_data()
        ui.notify(f"加载{'成功' if success else '失败'}")

# 启动时加载数据
print("应用启动，预加载数据...")
load_data()

if __name__ == "__main__":
    ui.run()
