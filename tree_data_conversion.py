import pandas as pd
from nicegui import ui

# 示例：您的原始数据结构
def get_sample_data():
    """模拟您的原始数据"""
    # 原始data列表
    data = [
        "项目A     编码001",
        "项目B     编码002", 
        "项目C     编码003"
    ]
    
    # 原始details字典
    details = {
        "项目A     编码001": {
            "rows": [
                {"name": "标段名称0", "value": "100万"},
                {"name": "标段名称1", "value": "200万"},
                {"name": "标段名称2", "value": "300万"}
            ]
        },
        "项目B     编码002": {
            "rows": [
                {"name": "标段名称0", "value": "150万"},
                {"name": "标段名称1", "value": "250万"},
                {"name": "标段名称2", "value": "350万"}
            ]
        },
        "项目C     编码003": {
            "rows": [
                {"name": "标段名称0", "value": "120万"},
                {"name": "标段名称1", "value": "220万"},
                {"name": "标段名称2", "value": "320万"}
            ]
        }
    }
    
    return data, details

def convert_to_tree_data_method1(data, details):
    """
    方法1: 使用orgHierarchy路径 (推荐用于AgGrid treeData)
    """
    tree_data = []
    
    for project in data:
        # 1. 添加父节点（项目）
        tree_data.append({
            "项目名称": project,
            "标段名称": "",
            "项目总投资": "",
            "type": "project",
            "orgHierarchy": [project]  # 树形路径
        })
        
        # 2. 添加子节点（标段）
        if project in details:
            for detail in details[project]["rows"]:
                tree_data.append({
                    "项目名称": "",  # 子节点项目名称为空
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "type": "detail",
                    "orgHierarchy": [project, detail["name"]]  # 父->子路径
                })
    
    return tree_data

def convert_to_tree_data_method2(data, details):
    """
    方法2: 使用嵌套children结构
    """
    tree_data = []
    
    for project in data:
        # 创建父节点
        project_node = {
            "项目名称": project,
            "标段名称": "",
            "项目总投资": "",
            "type": "project",
            "children": []
        }
        
        # 添加子节点
        if project in details:
            for detail in details[project]["rows"]:
                child_node = {
                    "项目名称": "",
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "type": "detail"
                }
                project_node["children"].append(child_node)
        
        tree_data.append(project_node)
    
    return tree_data

def convert_to_tree_data_method3(data, details):
    """
    方法3: 扁平化结构带层级标识
    """
    tree_data = []
    
    for i, project in enumerate(data):
        # 父节点
        tree_data.append({
            "id": f"project_{i}",
            "项目名称": project,
            "标段名称": "",
            "项目总投资": "",
            "level": 0,
            "parent_id": None,
            "is_expanded": True,
            "has_children": project in details and len(details[project]["rows"]) > 0
        })
        
        # 子节点
        if project in details:
            for j, detail in enumerate(details[project]["rows"]):
                tree_data.append({
                    "id": f"project_{i}_detail_{j}",
                    "项目名称": "",
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "level": 1,
                    "parent_id": f"project_{i}",
                    "is_expanded": False,
                    "has_children": False
                })
    
    return tree_data

def convert_to_tree_data_method4(data, details):
    """
    方法4: 适用于您的具体需求的转换
    """
    tree_data = []
    
    for project in data:
        # 解析项目名称和编码（如果需要）
        parts = project.split('     ')  # 根据您的数据格式
        project_name = parts[0] if len(parts) > 0 else project
        project_code = parts[1] if len(parts) > 1 else ""
        
        # 父节点 - 显示完整项目信息
        tree_data.append({
            "项目名称": project,  # 完整的项目信息
            "项目编码": project_code,
            "标段名称": "",
            "项目总投资": "",
            "类型": "项目",
            "层级": 0,
            "路径": [project]
        })
        
        # 子节点 - 标段信息
        if project in details:
            total_investment = 0
            for detail in details[project]["rows"]:
                # 尝试提取数值（如果投资是数字）
                try:
                    investment_value = float(detail["value"].replace('万', '')) if '万' in str(detail["value"]) else float(detail["value"])
                    total_investment += investment_value
                except:
                    investment_value = detail["value"]
                
                tree_data.append({
                    "项目名称": "",  # 子节点不显示项目名称
                    "项目编码": "",
                    "标段名称": detail["name"],
                    "项目总投资": detail["value"],
                    "类型": "标段",
                    "层级": 1,
                    "路径": [project, detail["name"]]
                })
            
            # 可选：更新父节点的总投资
            if total_investment > 0:
                tree_data[len(tree_data) - len(details[project]["rows"]) - 1]["项目总投资"] = f"{total_investment}万"
    
    return tree_data

# 演示所有转换方法
def demo_all_methods():
    """演示所有转换方法"""
    data, details = get_sample_data()
    
    print("=== 原始数据 ===")
    print("data:", data)
    print("details keys:", list(details.keys()))
    print("details示例:", details[data[0]])
    
    print("\n=== 方法1: orgHierarchy路径 ===")
    tree1 = convert_to_tree_data_method1(data, details)
    for item in tree1[:6]:  # 显示前6条
        print(item)
    
    print("\n=== 方法2: 嵌套children结构 ===")
    tree2 = convert_to_tree_data_method2(data, details)
    print(f"项目数: {len(tree2)}")
    print("第一个项目:", tree2[0])
    
    print("\n=== 方法3: 扁平化带层级 ===")
    tree3 = convert_to_tree_data_method3(data, details)
    for item in tree3[:4]:  # 显示前4条
        print(item)
    
    print("\n=== 方法4: 定制化转换 ===")
    tree4 = convert_to_tree_data_method4(data, details)
    for item in tree4[:6]:  # 显示前6条
        print(item)

# 实际使用示例
@ui.page("/")
def home():
    """在NiceGUI中使用tree_data"""
    
    # 获取您的数据
    data, details = get_sample_data()  # 替换为您的实际数据加载函数
    
    # 选择转换方法（推荐方法1）
    tree_data = convert_to_tree_data_method1(data, details)
    
    ui.label("AgGrid树形表格示例").classes("text-xl font-bold mb-4")
    
    # AgGrid配置
    grid_options = {
        "columnDefs": [
            {
                "headerName": "项目名称",
                "field": "项目名称",
                "width": 300,
                "cellRenderer": "agGroupCellRenderer"
            },
            {
                "headerName": "标段名称",
                "field": "标段名称",
                "width": 200
            },
            {
                "headerName": "项目总投资",
                "field": "项目总投资",
                "width": 150
            }
        ],
        "rowData": tree_data,
        "treeData": True,
        "animateRows": True,
        "groupDefaultExpanded": 1,
        "getDataPath": lambda data: data.get("orgHierarchy", []),
        "autoGroupColumnDef": {
            "headerName": "项目结构",
            "width": 250,
            "cellRendererParams": {"suppressCount": True}
        }
    }
    
    ui.aggrid(grid_options).classes("w-full h-96")

if __name__ == "__main__":
    # 运行演示
    demo_all_methods()
    
    # 启动UI
    ui.run()
