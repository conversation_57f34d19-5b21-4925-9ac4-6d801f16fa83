# Excel保存功能说明

## 概述

优化后的文件匹配系统现在支持将处理结果保存为详细的Excel文件，包含多个工作表和完整的统计信息。

## 输出文件

### 主要输出文件

1. **`稳定版匹配结果.xlsx`** - 稳定版本的输出
2. **`快速匹配结果.xlsx`** - 快速版本的输出

### 备选输出（当Excel保存失败时）

- `匹配结果.csv` - 匹配结果CSV格式
- `处理后数据.csv` - 处理数据CSV格式
- `未匹配文件.csv` - 未匹配文件CSV格式

## Excel文件结构

### 工作表详细说明

#### 1. 📋 详细匹配结果
包含完整的文件信息和匹配结果：

| 列名 | 说明 |
|------|------|
| 交付物标准 | 匹配到的标准名称 |
| 文件名 | 原始文件名 |
| 项目名称 | 所属项目名称 |
| 文件路径 | 完整文件路径 |
| 文件大小(万字节) | 文件大小 |
| 文件类型 | 文件类型 |
| 分部 | 所属部门 |
| 项目负责人 | 项目负责人 |
| 合同名称 | 关联合同 |
| 项目编码 | 项目编码 |

#### 2. 📊 匹配结果汇总
简化的匹配结果：

| 列名 | 说明 |
|------|------|
| 交付物标准 | 匹配到的标准名称 |
| 文件名 | 文件名 |
| 项目名称 | 项目名称 |

#### 3. 🗂️ 处理后数据
所有处理后的文件信息，包括：
- 文件路径、大小、类型
- 项目信息、部门信息
- 合同信息等

#### 4. ❌ 未匹配文件
未能匹配到标准的文件列表：

| 列名 | 说明 |
|------|------|
| 项目名称 | 项目名称 |
| 文件名 | 文件名 |

#### 5. 📝 交付物标准清单（仅稳定版）
所有交付物标准的完整清单：

| 列名 | 说明 |
|------|------|
| 类型 | 项目类型 |
| 交付物标准 | 标准名称 |

#### 6. 📈 统计汇总
详细的统计信息：

| 统计项目 | 数量 |
|----------|------|
| 总文件数 | 处理的总文件数 |
| 匹配成功 | 成功匹配的文件数 |
| 未匹配 | 未匹配的文件数 |
| 匹配成功率(%) | 匹配成功率 |
| --- 按交付物标准统计 --- | --- |
| 各个标准名称 | 对应匹配数量 |

## 使用方法

### 1. 运行稳定版本
```bash
python stable_matching_cpu.py
```

### 2. 运行快速版本
```bash
python fast_matching_cpu.py
```

### 3. 测试Excel保存功能
```bash
python test_excel_save.py
```

## 功能特点

### ✅ 完整性
- 包含所有处理结果
- 详细的文件信息
- 完整的统计数据

### ✅ 易读性
- 清晰的工作表分类
- 中文列名
- 合理的数据格式

### ✅ 实用性
- 支持Excel和CSV双格式
- 自动错误处理
- 详细的保存状态提示

### ✅ 兼容性
- 支持Excel 2007+格式
- UTF-8编码确保中文正确显示
- 跨平台兼容

## 错误处理

### 常见问题及解决方案

#### 1. Excel保存失败
**错误信息**: `保存Excel文件失败`

**可能原因**:
- openpyxl包未安装
- 文件被其他程序占用
- 磁盘空间不足

**解决方案**:
```bash
# 安装openpyxl
pip install openpyxl

# 或者使用conda
conda install openpyxl
```

#### 2. 中文显示乱码
**解决方案**: 程序已使用UTF-8编码，确保Excel版本支持UTF-8

#### 3. 文件过大
**解决方案**: 程序会自动尝试保存为CSV格式作为备选

## 性能优化

### 内存使用
- 分批处理大数据
- 及时清理临时数据
- 优化数据类型

### 保存速度
- 使用openpyxl引擎
- 批量写入数据
- 避免重复计算

## 自定义配置

### 修改输出文件名
在代码中修改：
```python
output_file = '自定义文件名.xlsx'
```

### 调整工作表内容
可以在 `save_results_to_excel` 方法中：
- 添加新的工作表
- 修改列名
- 调整统计内容

### 添加新的统计维度
在统计汇总部分添加：
```python
# 按部门统计
dept_stats = {}
for record in match_records:
    # 统计逻辑
    pass
```

## 依赖包要求

```
pandas>=1.3.0
openpyxl>=3.0.0
numpy>=1.21.0
```

## 示例输出

运行成功后会看到类似输出：
```
✅ 结果已成功保存到: 稳定版匹配结果.xlsx
📊 包含工作表:
   - 详细匹配结果: 包含文件路径、大小等详细信息
   - 匹配结果汇总: 简化的匹配结果
   - 处理后数据: 所有处理后的文件信息
   - 未匹配文件: 未能匹配到标准的文件
   - 交付物标准清单: 所有标准的清单
   - 统计汇总: 各种统计信息
```

## 注意事项

1. **文件路径**: 确保输入文件路径正确
2. **权限**: 确保有写入权限
3. **内存**: 大文件处理时注意内存使用
4. **编码**: 所有文本都使用UTF-8编码

## 技术支持

如果遇到问题：
1. 首先运行 `test_excel_save.py` 测试基本功能
2. 检查依赖包是否正确安装
3. 确认输入文件格式正确
4. 查看错误日志获取详细信息
