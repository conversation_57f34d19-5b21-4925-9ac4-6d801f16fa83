import pandas as pd

# 您的原始代码
dd1 = pd.read_excel(pp1, sheet_name='2025年B类新建', skiprows=[0,1,2])
ffill_col = ['项目名称','项目编码','建设单位','二级单位','建设性质','管理类别','项目性质','开始年','结束年','合计','资本性','费用性']
dd1[ffill_col] = dd1[ffill_col].ffill()

dd1项目名称 = dd1[['项目名称','项目编码']].drop_duplicates('项目名称').dropna().values.tolist()
ll1项目名称 = ['     '.join(map(str, row)) for row in dd1项目名称]
data = [i for i in ll1项目名称]

desired_cols = [
    "标段名称", "标段编号", "合同名称", "合同编号",
    "合同金额", "中标厂家", '是否跨项目签合同', '本标段对应合同金额'
]

# 获取实际存在的列（最多3个）
available_cols = [col for col in desired_cols if col in dd1.columns][:3]

# 将dd1的值代入到details的rows里面
details = {}
for row_key in data:
    # 提取项目名称
    project_name = row_key.split('     ')[0]
    
    # 在dd1中找到匹配的行
    matching_rows = dd1[dd1['项目名称'] == project_name]
    
    if not matching_rows.empty:
        # 使用第一个匹配的行数据
        row_data = matching_rows.iloc[0]
        
        details[row_key] = {
            "rows": [
                {
                    "name": available_cols[j] if j < len(available_cols) else f"字段{j+1}",
                    "value": str(row_data[available_cols[j]]) if j < len(available_cols) and pd.notna(row_data[available_cols[j]]) else ""
                }
                for j in range(3)
            ]
        }
    else:
        # 如果没找到匹配行，使用空值
        details[row_key] = {
            "rows": [
                {"name": available_cols[j] if j < len(available_cols) else f"字段{j+1}", "value": ""}
                for j in range(3)
            ]
        }

# 显示结果
print(f"使用的列: {available_cols}")
print("结果示例:")
for key, value in list(details.items())[:3]:
    print(f"{key}: {value}")
